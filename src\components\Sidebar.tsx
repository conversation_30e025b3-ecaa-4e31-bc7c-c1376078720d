import React from "react";
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Download,
  Mail,
  Phone,
  MapPin,
  Calendar,
} from "lucide-react";

const Sidebar = () => {
  return (
    <div className="bg-dark-lighter w-full lg:w-[380px] lg:min-h-screen lg:fixed lg:top-0 lg:left-0 p-8 overflow-y-auto">
      <div className="flex flex-col h-full">
        {/* Profile Image and Name */}
        <div className="text-center mb-8">
          <div className="mx-auto w-40 h-40 rounded-full overflow-hidden mb-6 ring-4 ring-red/20">
            <img
              src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face&auto=format&q=80"
              alt="<PERSON>"
              className="w-full h-full object-cover"
            />
          </div>
          <h1 className="text-white text-2xl font-bold"><PERSON></h1>
          <div className="typing-text mt-2 text-gray-300 text-center mx-auto max-w-[220px]">
            <span>Web Developer</span>
          </div>
        </div>

        {/* Social Icons */}
        <div className="flex justify-center gap-3 mb-8">
          <a href="#" className="social-icon">
            <Facebook size={18} />
          </a>
          <a href="#" className="social-icon">
            <Twitter size={18} />
          </a>
          <a href="#" className="social-icon">
            <Instagram size={18} />
          </a>
          <a href="#" className="social-icon">
            <Linkedin size={18} />
          </a>
        </div>

        {/* Contact Details */}
        <div className="space-y-4 mb-8">
          <div className="card flex items-center gap-4">
            <div className="min-w-10 h-10 rounded-lg bg-dark-bg flex items-center justify-center text-red">
              <Phone size={18} />
            </div>
            <div>
              <p className="text-sm text-gray-200 mb-0.5">Phone</p>
              <p className="text-gray-300 font-medium">+************</p>
            </div>
          </div>

          <div className="card flex items-center gap-4">
            <div className="min-w-10 h-10 rounded-lg bg-dark-bg flex items-center justify-center text-red">
              <Mail size={18} />
            </div>
            <div>
              <p className="text-sm text-gray-200 mb-0.5">Email</p>
              <p className="text-gray-300 font-medium"><EMAIL></p>
            </div>
          </div>

          <div className="card flex items-center gap-4">
            <div className="min-w-10 h-10 rounded-lg bg-dark-bg flex items-center justify-center text-red">
              <MapPin size={18} />
            </div>
            <div>
              <p className="text-sm text-gray-200 mb-0.5">Location</p>
              <p className="text-gray-300 font-medium">New York, USA</p>
            </div>
          </div>

          <div className="card flex items-center gap-4">
            <div className="min-w-10 h-10 rounded-lg bg-dark-bg flex items-center justify-center text-red">
              <Calendar size={18} />
            </div>
            <div>
              <p className="text-sm text-gray-200 mb-0.5">Birthday</p>
              <p className="text-gray-300 font-medium">May 27, 1990</p>
            </div>
          </div>
        </div>

        {/* Download CV Button */}
        <div className="mt-auto">
          <a
            href="#"
            className="btn btn-primary w-full flex items-center justify-center gap-2"
          >
            <Download size={18} />
            <span>Download CV</span>
          </a>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
