export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      attendance_alerts: {
        Row: {
          alert_type: string
          created_at: string
          distance_meters: number | null
          id: string
          reviewed_at: string | null
          reviewed_by: string | null
          room_id: string
          room_location: Json | null
          status: string | null
          student_id: string
          student_location: Json | null
          timestamp: string
        }
        Insert: {
          alert_type: string
          created_at?: string
          distance_meters?: number | null
          id?: string
          reviewed_at?: string | null
          reviewed_by?: string | null
          room_id: string
          room_location?: Json | null
          status?: string | null
          student_id: string
          student_location?: Json | null
          timestamp?: string
        }
        Update: {
          alert_type?: string
          created_at?: string
          distance_meters?: number | null
          id?: string
          reviewed_at?: string | null
          reviewed_by?: string | null
          room_id?: string
          room_location?: Json | null
          status?: string | null
          student_id?: string
          student_location?: Json | null
          timestamp?: string
        }
        Relationships: [
          {
            foreignKeyName: "attendance_alerts_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "rooms"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_records: {
        Row: {
          created_at: string | null
          device_info: string | null
          id: string
          location: Json | null
          room_id: string
          status: string
          student_id: string
          timestamp: string | null
          verification_method: string
        }
        Insert: {
          created_at?: string | null
          device_info?: string | null
          id?: string
          location?: Json | null
          room_id: string
          status: string
          student_id: string
          timestamp?: string | null
          verification_method: string
        }
        Update: {
          created_at?: string | null
          device_info?: string | null
          id?: string
          location?: Json | null
          room_id?: string
          status?: string
          student_id?: string
          timestamp?: string | null
          verification_method?: string
        }
        Relationships: [
          {
            foreignKeyName: "attendance_records_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "rooms"
            referencedColumns: ["id"]
          },
        ]
      }
      biometric_credentials: {
        Row: {
          counter: number | null
          created_at: string | null
          credential_id: string
          id: string
          public_key: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          counter?: number | null
          created_at?: string | null
          credential_id: string
          id?: string
          public_key: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          counter?: number | null
          created_at?: string | null
          credential_id?: string
          id?: string
          public_key?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      blocks: {
        Row: {
          created_at: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      notifications: {
        Row: {
          admin_read_at: string | null
          created_at: string | null
          distance_meters: number | null
          id: string
          message: string
          metadata: Json | null
          read: boolean | null
          resolved_by: string | null
          room_number: string | null
          student_id: string | null
          student_location: unknown | null
          teacher_id: string | null
          teacher_read_at: string | null
          timestamp: string | null
          title: string
          type: Database["public"]["Enums"]["notification_type"]
          updated_at: string | null
        }
        Insert: {
          admin_read_at?: string | null
          created_at?: string | null
          distance_meters?: number | null
          id?: string
          message: string
          metadata?: Json | null
          read?: boolean | null
          resolved_by?: string | null
          room_number?: string | null
          student_id?: string | null
          student_location?: unknown | null
          teacher_id?: string | null
          teacher_read_at?: string | null
          timestamp?: string | null
          title: string
          type: Database["public"]["Enums"]["notification_type"]
          updated_at?: string | null
        }
        Update: {
          admin_read_at?: string | null
          created_at?: string | null
          distance_meters?: number | null
          id?: string
          message?: string
          metadata?: Json | null
          read?: boolean | null
          resolved_by?: string | null
          room_number?: string | null
          student_id?: string | null
          student_location?: unknown | null
          teacher_id?: string | null
          teacher_read_at?: string | null
          timestamp?: string | null
          title?: string
          type?: Database["public"]["Enums"]["notification_type"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_resolved_by_fkey"
            columns: ["resolved_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "notifications_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "notifications_teacher_id_fkey"
            columns: ["teacher_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
        ]
      }
      profiles: {
        Row: {
          access_level: number | null
          admin_id: string | null
          biometric_registered: boolean | null
          block_id: string | null
          block_name: string | null
          course: string | null
          created_at: string | null
          department: string | null
          email: string | null
          id: string
          name: string | null
          photo_url: string | null
          pin: string | null
          position: string | null
          role: string | null
          room_id: string | null
          room_number: string | null
          student_id: string | null
          subject: string | null
          teacher_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          access_level?: number | null
          admin_id?: string | null
          biometric_registered?: boolean | null
          block_id?: string | null
          block_name?: string | null
          course?: string | null
          created_at?: string | null
          department?: string | null
          email?: string | null
          id: string
          name?: string | null
          photo_url?: string | null
          pin?: string | null
          position?: string | null
          role?: string | null
          room_id?: string | null
          room_number?: string | null
          student_id?: string | null
          subject?: string | null
          teacher_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          access_level?: number | null
          admin_id?: string | null
          biometric_registered?: boolean | null
          block_id?: string | null
          block_name?: string | null
          course?: string | null
          created_at?: string | null
          department?: string | null
          email?: string | null
          id?: string
          name?: string | null
          photo_url?: string | null
          pin?: string | null
          position?: string | null
          role?: string | null
          room_id?: string | null
          room_number?: string | null
          student_id?: string | null
          subject?: string | null
          teacher_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_block_id_fkey"
            columns: ["block_id"]
            isOneToOne: false
            referencedRelation: "blocks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "rooms"
            referencedColumns: ["id"]
          },
        ]
      }
      room_locations: {
        Row: {
          id: string
          last_updated: string | null
          latitude: number
          longitude: number
          radius_meters: number
          room_id: string
          updated_by: string | null
        }
        Insert: {
          id?: string
          last_updated?: string | null
          latitude: number
          longitude: number
          radius_meters?: number
          room_id: string
          updated_by?: string | null
        }
        Update: {
          id?: string
          last_updated?: string | null
          latitude?: number
          longitude?: number
          radius_meters?: number
          room_id?: string
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "room_locations_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: true
            referencedRelation: "rooms"
            referencedColumns: ["id"]
          },
        ]
      }
      rooms: {
        Row: {
          block_id: string
          building: string | null
          capacity: number | null
          created_at: string | null
          current_qr_code: string | null
          floor: number | null
          id: string
          name: string
          qr_expiry: string | null
          teacher_id: string | null
          updated_at: string | null
        }
        Insert: {
          block_id: string
          building?: string | null
          capacity?: number | null
          created_at?: string | null
          current_qr_code?: string | null
          floor?: number | null
          id?: string
          name: string
          qr_expiry?: string | null
          teacher_id?: string | null
          updated_at?: string | null
        }
        Update: {
          block_id?: string
          building?: string | null
          capacity?: number | null
          created_at?: string | null
          current_qr_code?: string | null
          floor?: number | null
          id?: string
          name?: string
          qr_expiry?: string | null
          teacher_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "rooms_block_id_fkey"
            columns: ["block_id"]
            isOneToOne: false
            referencedRelation: "blocks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rooms_teacher_id_fkey"
            columns: ["teacher_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      calculate_distance: {
        Args: { lat1: number; lon1: number; lat2: number; lon2: number }
        Returns: number
      }
      check_profile_permissions: {
        Args: { target_user_id: string }
        Returns: Json
      }
      create_room_locations_table: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      debug_profile_state: {
        Args: { target_user_id: string }
        Returns: Json
      }
      delete_user: {
        Args: { target_user_id: string }
        Returns: undefined
      }
      handle_distance_alert: {
        Args:
          | {
              student_id: string
              student_lat: number
              student_lng: number
              room_lat: number
              room_lng: number
              max_distance_meters: number
            }
          | {
              student_id: string
              student_lat: number
              student_lng: number
              room_lat: number
              room_lng: number
              max_distance_meters: number
              room_number: string
            }
        Returns: {
          is_within_radius: boolean
          distance: number
          alert_id: string
        }[]
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      refresh_all_jwt_claims: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      refresh_jwt_claims: {
        Args: { target_user_id: string }
        Returns: undefined
      }
      verify_attendance_location: {
        Args: { student_lat: number; student_lon: number; room_id: string }
        Returns: boolean
      }
    }
    Enums: {
      alert_category: "fraud_detection" | "distance_alert" | "general"
      notification_type:
        | "distance_alert"
        | "system_alert"
        | "attendance_alert"
        | "attendance"
        | "absence"
        | "late"
        | "excused"
        | "system"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      alert_category: ["fraud_detection", "distance_alert", "general"],
      notification_type: [
        "distance_alert",
        "system_alert",
        "attendance_alert",
        "attendance",
        "absence",
        "late",
        "excused",
        "system",
      ],
    },
  },
} as const
