
import React, { useState } from 'react';

const Portfolio = () => {
  // Categories for filtering
  const categories = ['All', 'Web Design', 'Mobile App', 'Dashboard', 'Landing Page'];
  const [activeCategory, setActiveCategory] = useState('All');

  // Portfolio items
  const portfolioItems = [
    {
      id: 1,
      title: 'Mobile App Design',
      category: 'Mobile App',
      image: 'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-01.jpg',
    },
    {
      id: 2,
      title: 'Website Redesign',
      category: 'Web Design',
      image: 'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-02.jpg',
    },
    {
      id: 3,
      title: 'Dashboard Design',
      category: 'Dashboard',
      image: 'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-03.jpg',
    },
    {
      id: 4,
      title: 'Landing Page Design',
      category: 'Landing Page',
      image: 'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-04.jpg',
    },
    {
      id: 5,
      title: 'Mobile UI Design',
      category: 'Mobile App',
      image: 'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-05.jpg',
    },
    {
      id: 6,
      title: 'Corporate Website',
      category: 'Web Design',
      image: 'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-06.jpg',
    }
  ];

  // Filter portfolio items based on active category
  const filteredItems = activeCategory === 'All' 
    ? portfolioItems 
    : portfolioItems.filter(item => item.category === activeCategory);

  return (
    <section id="portfolio" className="section">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="heading-title">My Portfolio</h2>
          <p className="text-gray-200 max-w-2xl mx-auto">
            Check out some of my latest projects. I've worked on various types of designs including web design, mobile apps, and more.
          </p>
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-3 mb-10">
          {categories.map((category, index) => (
            <button
              key={index}
              className={`portfolio-filter-btn ${activeCategory === category ? 'bg-red text-white' : ''}`}
              onClick={() => setActiveCategory(category)}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Portfolio Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map(item => (
            <div key={item.id} className="group">
              <div className="card p-0 overflow-hidden">
                <div className="relative overflow-hidden">
                  <img 
                    src={item.image} 
                    alt={item.title} 
                    className="w-full h-64 object-cover group-hover:scale-105 transition-all duration-500"
                  />
                  <div className="absolute inset-0 bg-black/80 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <div className="text-center p-4 transform translate-y-8 group-hover:translate-y-0 transition-transform duration-300">
                      <h4 className="text-white text-xl font-semibold mb-2">{item.title}</h4>
                      <p className="text-red">{item.category}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
