import React, { useState, useRef, useEffect } from "react";

const Portfolio = () => {
  // Categories for filtering
  const categories = [
    "All",
    "Web Design",
    "Mobile App",
    "Dashboard",
    "Landing Page",
  ];
  const [activeCategory, setActiveCategory] = useState("All");
  const [isAnimating, setIsAnimating] = useState(false);
  const portfolioRef = useRef<HTMLElement>(null);

  // Portfolio items
  const portfolioItems = [
    {
      id: 1,
      title: "Mobile App Design",
      category: "Mobile App",
      description: "Modern iOS & Android app with stunning UI/UX",
      technologies: ["React Native", "TypeScript", "Figma"],
      image:
        "https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-01.jpg",
      year: "2024",
    },
    {
      id: 2,
      title: "Website Redesign",
      category: "Web Design",
      description: "Complete website overhaul with modern design",
      technologies: ["React", "Tailwind CSS", "Framer Motion"],
      image:
        "https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-02.jpg",
      year: "2024",
    },
    {
      id: 3,
      title: "Dashboard Design",
      category: "Dashboard",
      description: "Analytics dashboard with real-time data visualization",
      technologies: ["Vue.js", "Chart.js", "Node.js"],
      image:
        "https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-03.jpg",
      year: "2023",
    },
    {
      id: 4,
      title: "Landing Page Design",
      category: "Landing Page",
      description: "High-converting landing page for SaaS product",
      technologies: ["Next.js", "GSAP", "Stripe"],
      image:
        "https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-04.jpg",
      year: "2024",
    },
    {
      id: 5,
      title: "Mobile UI Design",
      category: "Mobile App",
      description: "E-commerce mobile app with seamless UX",
      technologies: ["Flutter", "Firebase", "Stripe"],
      image:
        "https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-05.jpg",
      year: "2023",
    },
    {
      id: 6,
      title: "Corporate Website",
      category: "Web Design",
      description: "Professional corporate website with CMS",
      technologies: ["WordPress", "PHP", "MySQL"],
      image:
        "https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-06.jpg",
      year: "2023",
    },
  ];

  // Filter portfolio items based on active category
  const filteredItems =
    activeCategory === "All"
      ? portfolioItems
      : portfolioItems.filter((item) => item.category === activeCategory);

  // Handle category change with animation
  const handleCategoryChange = (category: string) => {
    if (category === activeCategory) return;

    setIsAnimating(true);
    setTimeout(() => {
      setActiveCategory(category);
      setIsAnimating(false);
    }, 300);
  };

  return (
    <section
      id="portfolio"
      ref={portfolioRef}
      className="section bg-dark-darker relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="portfolio-bg-glow"></div>
      <div className="portfolio-particles"></div>
      <div className="portfolio-grid-pattern"></div>

      <div className="container mx-auto relative z-10">
        {/* Enhanced Title Section */}
        <div className="text-center mb-16">
          <div className="relative inline-block">
            <h2 className="enhanced-portfolio-title">My Portfolio</h2>
            <div className="portfolio-title-glow"></div>
            <div className="portfolio-title-underline"></div>
          </div>

          <div className="portfolio-description-container">
            <p className="portfolio-description">
              <span className="highlight-text">Discover</span> my latest{" "}
              <span className="highlight-text">creative works</span> and{" "}
              <span className="highlight-text">innovative solutions</span>{" "}
              across various digital platforms and technologies.
            </p>
          </div>
        </div>

        {/* Enhanced Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-16">
          {categories.map((category, index) => (
            <button
              key={index}
              className={`enhanced-filter-btn ${
                activeCategory === category ? "active" : ""
              }`}
              onClick={() => handleCategoryChange(category)}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <span className="filter-btn-text">{category}</span>
              <div className="filter-btn-glow"></div>
              <div className="filter-btn-particles"></div>
            </button>
          ))}
        </div>

        {/* Enhanced Portfolio Grid */}
        <div className={`portfolio-grid ${isAnimating ? "animating" : ""}`}>
          {filteredItems.map((item, index) => (
            <div
              key={item.id}
              className="enhanced-portfolio-item"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="portfolio-card">
                <div className="portfolio-image-container">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="portfolio-image"
                    loading="lazy"
                  />
                  <div className="portfolio-overlay">
                    <div className="portfolio-content">
                      <div className="portfolio-year">{item.year}</div>
                      <h4 className="portfolio-title">{item.title}</h4>
                      <p className="portfolio-description">
                        {item.description}
                      </p>
                      <div className="portfolio-technologies">
                        {item.technologies.map((tech, techIndex) => (
                          <span key={techIndex} className="tech-badge">
                            {tech}
                          </span>
                        ))}
                      </div>
                      <div className="portfolio-category">{item.category}</div>
                    </div>
                  </div>
                  <div className="portfolio-glow"></div>
                  <div className="portfolio-particles"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
