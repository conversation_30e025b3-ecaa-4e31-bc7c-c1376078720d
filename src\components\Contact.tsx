
import React, { useState } from 'react';
import { Phone, Mail, MapPin, Send } from 'lucide-react';
import { toast } from 'sonner';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      toast.success('Message sent successfully!', {
        description: 'We will get back to you soon.'
      });
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
      setIsSubmitting(false);
    }, 1000);
  };

  // Contact info data
  const contactInfo = [
    {
      icon: <Phone size={20} />,
      title: 'Phone',
      content: '+************',
      href: 'tel:+1234567890'
    },
    {
      icon: <Mail size={20} />,
      title: 'Email',
      content: '<EMAIL>',
      href: 'mailto:<EMAIL>'
    },
    {
      icon: <MapPin size={20} />,
      title: 'Address',
      content: 'New York City, USA',
      href: 'https://maps.google.com'
    }
  ];

  return (
    <section id="contact" className="section">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="heading-title">Contact Me</h2>
          <p className="text-gray-200 max-w-2xl mx-auto">
            Feel free to contact me for any project or collaboration.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Contact Info */}
          <div className="space-y-6">
            {contactInfo.map((info, index) => (
              <a 
                key={index} 
                href={info.href}
                className="card flex items-start gap-4 hover:no-underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                <div className="w-10 h-10 rounded-lg bg-dark-bg flex items-center justify-center text-red mt-1">
                  {info.icon}
                </div>
                <div>
                  <p className="text-gray-300 font-medium mb-1">{info.title}</p>
                  <p className="text-gray-200">{info.content}</p>
                </div>
              </a>
            ))}
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit} className="card">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label htmlFor="name" className="block text-gray-300 mb-2">Your Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full bg-dark-bg border border-gray-300/20 rounded-lg px-4 py-3 text-gray-300 focus:outline-none focus:border-red"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-gray-300 mb-2">Email Address</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full bg-dark-bg border border-gray-300/20 rounded-lg px-4 py-3 text-gray-300 focus:outline-none focus:border-red"
                  />
                </div>
                <div>
                  <label htmlFor="phone" className="block text-gray-300 mb-2">Phone Number</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full bg-dark-bg border border-gray-300/20 rounded-lg px-4 py-3 text-gray-300 focus:outline-none focus:border-red"
                  />
                </div>
                <div>
                  <label htmlFor="subject" className="block text-gray-300 mb-2">Subject</label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    className="w-full bg-dark-bg border border-gray-300/20 rounded-lg px-4 py-3 text-gray-300 focus:outline-none focus:border-red"
                  />
                </div>
              </div>
              <div className="mb-6">
                <label htmlFor="message" className="block text-gray-300 mb-2">Your Message</label>
                <textarea
                  id="message"
                  name="message"
                  rows={5}
                  value={formData.message}
                  onChange={handleChange}
                  required
                  className="w-full bg-dark-bg border border-gray-300/20 rounded-lg px-4 py-3 text-gray-300 focus:outline-none focus:border-red resize-none"
                ></textarea>
              </div>
              <button 
                type="submit" 
                className="btn btn-primary w-full flex items-center justify-center gap-2"
                disabled={isSubmitting}
              >
                <Send size={18} />
                <span>{isSubmitting ? 'Sending...' : 'Send Message'}</span>
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
