// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://wclwxrilybnzkhvqzbmy.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndjbHd4cmlseWJuemtodnF6Ym15Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzA5NTksImV4cCI6MjA2MTI0Njk1OX0.MIARsz34RX0EftvwUkWIrEYQqE8VstxaCI31mjLhSHw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);