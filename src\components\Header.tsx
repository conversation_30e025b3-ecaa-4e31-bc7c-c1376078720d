import React, { useState, useEffect } from "react";
import {
  Menu,
  X,
  Home,
  User,
  FileText,
  Briefcase,
  MessageSquare,
  Star,
  Github,
  Linkedin,
  Twitter,
  Mail,
} from "lucide-react";
import {
  Drawer,
  DrawerContent,
  Drawer<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "./ui/drawer";
import { Avatar, AvatarImage, AvatarFallback } from "./ui/avatar";

const Header = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState("home");
  const [scrolled, setScrolled] = useState(false);
  const [typingIndex, setTypingIndex] = useState(0);

  const navItems = [
    { id: "home", label: "Home", icon: Home },
    { id: "about", label: "About", icon: User },
    { id: "resume", label: "Resume", icon: FileText },
    { id: "portfolio", label: "Portfolio", icon: Briefcase },
    { id: "testimonials", label: "Testimonials", icon: Star },
    { id: "contact", label: "Contact", icon: MessageSquare },
  ];

  const socialLinks = [
    { icon: Github, href: "https://github.com", label: "GitHub" },
    { icon: Linkedin, href: "https://linkedin.com", label: "LinkedIn" },
    { icon: Twitter, href: "https://twitter.com", label: "Twitter" },
    { icon: Mail, href: "mailto:<EMAIL>", label: "Email" },
  ];

  const profession = "Web Developer";

  useEffect(() => {
    // Handle typing animation for profession text in mobile menu
    if (mobileMenuOpen) {
      const typingInterval = setInterval(() => {
        setTypingIndex((prev) => {
          if (prev >= profession.length) {
            // Reset after showing full text for 1.5s
            setTimeout(() => setTypingIndex(0), 1500);
            return prev;
          }
          return prev + 1;
        });
      }, 150);

      return () => clearInterval(typingInterval);
    } else {
      // Reset typing index when menu is closed
      setTypingIndex(0);
    }
  }, [mobileMenuOpen]);

  useEffect(() => {
    const handleScroll = () => {
      // Update header background when scrolled
      setScrolled(window.scrollY > 50);

      // Update active section based on scroll position
      const sections = navItems.map((item) => document.getElementById(item.id));
      let currentActive = "home";

      sections.forEach((section) => {
        if (section) {
          const sectionTop = section.offsetTop;
          const sectionHeight = section.offsetHeight;
          if (
            window.scrollY >= sectionTop - 100 &&
            window.scrollY < sectionTop + sectionHeight - 100
          ) {
            currentActive = section.id;
          }
        }
      });

      setActiveSection(currentActive);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.classList.add("mobile-menu-open");
    } else {
      document.body.classList.remove("mobile-menu-open");
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove("mobile-menu-open");
    };
  }, [mobileMenuOpen]);

  // Handle mobile menu link click - close drawer and scroll to section
  const handleMobileNavClick = (id: string) => {
    setMobileMenuOpen(false);
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <header
      className={`fixed top-0 right-0 left-0 lg:left-[380px] z-50 transition-all duration-300 ${
        scrolled ? "bg-dark-bg/90 backdrop-blur-sm shadow-lg" : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <Drawer
              open={mobileMenuOpen}
              onOpenChange={setMobileMenuOpen}
              direction="right"
            >
              <DrawerTrigger asChild>
                <button
                  className="relative w-10 h-10 flex flex-col justify-center items-center text-gray-300 hover:text-red focus:outline-none transition-all duration-300 group"
                  aria-label="Toggle navigation menu"
                >
                  {/* Animated Hamburger Lines */}
                  <span
                    className={`hamburger-line block w-6 h-0.5 bg-current ${
                      mobileMenuOpen ? "rotate-45 translate-y-1.5" : ""
                    }`}
                  ></span>
                  <span
                    className={`hamburger-line block w-6 h-0.5 bg-current mt-1.5 ${
                      mobileMenuOpen ? "opacity-0" : ""
                    }`}
                  ></span>
                  <span
                    className={`hamburger-line block w-6 h-0.5 bg-current mt-1.5 ${
                      mobileMenuOpen ? "-rotate-45 -translate-y-1.5" : ""
                    }`}
                  ></span>
                </button>
              </DrawerTrigger>
              <DrawerOverlay
                className="fixed inset-0 z-50 bg-transparent"
                onClick={() => setMobileMenuOpen(false)}
              />
              <DrawerContent
                className="fixed inset-y-0 left-0 z-50 mobile-menu-full-height w-80 max-w-[80vw] border-r border-gray-800/50 bg-gradient-to-br from-dark-bg/95 to-dark-lighter/95 backdrop-blur-xl shadow-2xl transition-all duration-500 ease-out mobile-menu-content overflow-hidden"
                style={{
                  transform: mobileMenuOpen
                    ? "translateX(0)"
                    : "translateX(-100%)",
                }}
              >
                <div className="flex flex-col mobile-menu-full-height relative overflow-hidden">
                  {/* Gradient Background Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-b from-red/5 via-transparent to-red/10 pointer-events-none"></div>

                  {/* Header with Close Button */}
                  <div className="flex justify-between items-center p-6 pb-4 relative z-10 flex-shrink-0">
                    <div className="w-8 h-8"></div> {/* Spacer */}
                    <button
                      onClick={() => setMobileMenuOpen(false)}
                      className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800/50 text-gray-300 hover:text-red hover:bg-red/10 focus:outline-none transition-all duration-300 backdrop-blur-sm"
                    >
                      <X size={20} />
                    </button>
                  </div>

                  {/* Scrollable Content */}
                  <div className="flex-1 overflow-y-auto relative z-10 scrollbar-thin scrollbar-thumb-red/30 scrollbar-track-transparent">
                    {/* Profile Section */}
                    <div className="profile-section flex flex-col items-center px-6 pb-6 flex-shrink-0">
                      <div className="relative mb-6">
                        <Avatar className="h-20 w-20 ring-2 ring-red/30 shadow-lg">
                          <AvatarImage
                            src="https://rainbowit.net/html/inbio/assets/images/slider/banner-01.png"
                            alt="John Smith"
                          />
                          <AvatarFallback className="bg-red/20 text-red">
                            JS
                          </AvatarFallback>
                        </Avatar>
                        <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-dark-bg animate-pulse"></div>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-1">
                        John Smith
                      </h3>
                      <div className="h-5 relative overflow-hidden">
                        <span className="text-red text-sm font-medium">
                          {profession.substring(0, typingIndex)}
                          <span className="animate-pulse">|</span>
                        </span>
                      </div>
                    </div>

                    {/* Navigation Links */}
                    <nav className="px-6 pb-6">
                      <ul className="flex flex-col space-y-2">
                        {navItems.map((item, index) => {
                          const IconComponent = item.icon;
                          return (
                            <li
                              key={item.id}
                              className="mobile-nav-item opacity-0"
                            >
                              <button
                                onClick={() => handleMobileNavClick(item.id)}
                                className={`w-full flex items-center gap-4 p-4 rounded-xl transition-all duration-300 group transform hover:scale-105 ${
                                  activeSection === item.id
                                    ? "bg-red/20 text-red border border-red/30 shadow-lg shadow-red/10 nav-item-active"
                                    : "text-gray-300 hover:text-white hover:bg-gray-800/50 border border-transparent hover:border-gray-700/50"
                                }`}
                              >
                                <div
                                  className={`w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300 ${
                                    activeSection === item.id
                                      ? "bg-red/30 text-red"
                                      : "bg-gray-800/50 text-gray-400 group-hover:bg-red/20 group-hover:text-red"
                                  }`}
                                >
                                  <IconComponent size={18} />
                                </div>
                                <span className="font-medium text-base">
                                  {item.label}
                                </span>
                                <div
                                  className={`ml-auto w-2 h-2 rounded-full transition-all duration-300 ${
                                    activeSection === item.id
                                      ? "bg-red"
                                      : "bg-transparent"
                                  }`}
                                ></div>
                              </button>
                            </li>
                          );
                        })}
                      </ul>
                    </nav>

                    {/* Social Links */}
                    <div className="px-6 pb-6 mt-8">
                      <div className="border-t border-gray-800/50 pt-6">
                        <p className="text-gray-400 text-sm mb-4 text-center">
                          Connect with me
                        </p>
                        <div className="flex justify-center gap-3">
                          {socialLinks.map((social, index) => {
                            const IconComponent = social.icon;
                            return (
                              <a
                                key={index}
                                href={social.href}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="social-link w-10 h-10 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-red hover:bg-red/20"
                                aria-label={social.label}
                              >
                                <IconComponent size={16} />
                              </a>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </DrawerContent>
            </Drawer>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:block">
            <ul className="flex space-x-2">
              {navItems.map((item) => (
                <li key={item.id}>
                  <a
                    href={`#${item.id}`}
                    className={`nav-link-dash relative ${
                      activeSection === item.id ? "text-red" : "text-gray-300"
                    }`}
                  >
                    {item.label}
                    {activeSection === item.id && (
                      <span
                        className="absolute -bottom-1 left-0 h-0.5 bg-red"
                        style={{
                          width: "0%",
                          animation: "nav-dash 0.5s forwards",
                        }}
                      ></span>
                    )}
                  </a>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
