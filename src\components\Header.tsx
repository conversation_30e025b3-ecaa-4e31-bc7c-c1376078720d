
import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import { Drawer, DrawerContent, DrawerOverlay, DrawerTrigger } from './ui/drawer';
import { Avatar, AvatarImage, AvatarFallback } from './ui/avatar';

const Header = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState("home");
  const [scrolled, setScrolled] = useState(false);
  const [typingIndex, setTypingIndex] = useState(0);

  const navItems = [
    { id: 'home', label: 'Home' },
    { id: 'about', label: 'About' },
    { id: 'resume', label: 'Resume' },
    { id: 'portfolio', label: 'Portfolio' },
    { id: 'testimonials', label: 'Testimonials' },
    { id: 'contact', label: 'Contact' }
  ];

  const profession = "Web Developer";

  useEffect(() => {
    // Handle typing animation for profession text in mobile menu
    if (mobileMenuOpen) {
      const typingInterval = setInterval(() => {
        setTypingIndex(prev => {
          if (prev >= profession.length) {
            // Reset after showing full text for 1.5s
            setTimeout(() => setTypingIndex(0), 1500);
            return prev;
          }
          return prev + 1;
        });
      }, 150);

      return () => clearInterval(typingInterval);
    } else {
      // Reset typing index when menu is closed
      setTypingIndex(0);
    }
  }, [mobileMenuOpen]);

  useEffect(() => {
    const handleScroll = () => {
      // Update header background when scrolled
      setScrolled(window.scrollY > 50);
      
      // Update active section based on scroll position
      const sections = navItems.map(item => document.getElementById(item.id));
      let currentActive = 'home';
      
      sections.forEach(section => {
        if (section) {
          const sectionTop = section.offsetTop;
          const sectionHeight = section.offsetHeight;
          if (window.scrollY >= sectionTop - 100 && window.scrollY < sectionTop + sectionHeight - 100) {
            currentActive = section.id;
          }
        }
      });
      
      setActiveSection(currentActive);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle mobile menu link click - close drawer and scroll to section
  const handleMobileNavClick = (id: string) => {
    setMobileMenuOpen(false);
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <header className={`fixed top-0 right-0 left-0 lg:left-[380px] z-50 transition-all duration-300 ${scrolled ? 'bg-dark-bg/90 backdrop-blur-sm shadow-lg' : 'bg-transparent'}`}>
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <Drawer open={mobileMenuOpen} onOpenChange={setMobileMenuOpen} direction="right">
              <DrawerTrigger asChild>
                <button 
                  className="text-gray-300 hover:text-red focus:outline-none"
                  aria-label="Toggle navigation menu"
                >
                  <Menu size={24} />
                </button>
              </DrawerTrigger>
              <DrawerOverlay className="bg-black/70" />
              <DrawerContent className="fixed inset-y-0 left-0 h-full w-80 border-r border-gray-800 bg-dark-bg shadow-xl transition-transform duration-300" 
                style={{ 
                  transform: mobileMenuOpen ? 'translateX(0)' : 'translateX(-100%)',
                  animation: mobileMenuOpen ? 'slide-in-left 0.3s ease-out forwards' : 'slide-out-left 0.3s ease-out forwards'
                }}>
                <div className="flex flex-col h-full p-6">
                  <div className="flex justify-end mb-6">
                    <button
                      onClick={() => setMobileMenuOpen(false)}
                      className="text-gray-300 hover:text-red focus:outline-none"
                    >
                      <X size={24} />
                    </button>
                  </div>
                  
                  {/* Profile Section */}
                  <div className="flex flex-col items-center mb-8">
                    <Avatar className="h-24 w-24 mb-4 ring-2 ring-red/20">
                      <AvatarImage 
                        src="https://rainbowit.net/html/inbio/assets/images/slider/banner-01.png" 
                        alt="John Smith" 
                      />
                      <AvatarFallback>JS</AvatarFallback>
                    </Avatar>
                    <h3 className="text-xl font-bold text-white mb-2">John Smith</h3>
                    <div className="h-6 relative overflow-hidden">
                      <span className="text-red">
                        {profession.substring(0, typingIndex)}
                        <span className="animate-pulse">|</span>
                      </span>
                    </div>
                  </div>
                  
                  {/* Navigation Links */}
                  <nav className="mt-6 flex-grow">
                    <ul className="flex flex-col space-y-4">
                      {navItems.map(item => (
                        <li key={item.id} className="border-b border-gray-800 pb-2">
                          <button
                            onClick={() => handleMobileNavClick(item.id)}
                            className={`text-xl font-medium ${activeSection === item.id ? 'text-red' : 'text-gray-300'} hover:text-red transition-colors w-full text-left`}
                          >
                            {item.label}
                          </button>
                        </li>
                      ))}
                    </ul>
                  </nav>
                </div>
              </DrawerContent>
            </Drawer>
          </div>
          
          {/* Desktop Navigation */}
          <nav className="hidden lg:block">
            <ul className="flex space-x-2">
              {navItems.map(item => (
                <li key={item.id}>
                  <a 
                    href={`#${item.id}`}
                    className={`nav-link-dash relative ${activeSection === item.id ? 'text-red' : 'text-gray-300'}`}
                  >
                    {item.label}
                    {activeSection === item.id && (
                      <span className="absolute -bottom-1 left-0 h-0.5 bg-red" 
                            style={{ 
                              width: '0%',
                              animation: 'nav-dash 0.5s forwards'
                            }}></span>
                    )}
                  </a>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
