
import React from 'react';
import { Star } from 'lucide-react';

const Testimonials = () => {
  // Testimonial data with updated image paths
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      position: "CEO at Apple",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&q=80",
      text: "<PERSON> was a real pleasure to work with and we look forward to working with him again. He's definitely the kind of designer you can trust with a project from start to finish.",
      rating: 5
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "CTO at Google",
      image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=300&h=300&fit=crop&q=80",
      text: "Highly recommend <PERSON>. His expertise in web development is exceptional. He delivered beyond our expectations and was a pleasure to work with throughout the project.",
      rating: 5
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "Marketing Director",
      image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=300&h=300&fit=crop&q=80",
      text: "Working with <PERSON> was an excellent experience. He understood our requirements perfectly and delivered a beautiful website that exceeded our expectations.",
      rating: 4
    },
  ];

  return (
    <section id="testimonials" className="section bg-dark-darker">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="heading-title">Testimonials</h2>
          <p className="text-gray-200 max-w-2xl mx-auto">
            What my clients say about my work and expertise.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="card">
              <div className="flex gap-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star 
                    key={i} 
                    size={16} 
                    className={`${i < testimonial.rating ? 'text-red' : 'text-gray-200'} fill-current`} 
                  />
                ))}
              </div>
              
              <p className="text-gray-200 mb-6 min-h-[100px]">{testimonial.text}</p>
              
              <div className="flex items-center">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4 border-2 border-red">
                  <img 
                    src={testimonial.image} 
                    alt={testimonial.name} 
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>
                <div>
                  <h5 className="text-gray-300 font-medium">{testimonial.name}</h5>
                  <p className="text-red text-sm">{testimonial.position}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
