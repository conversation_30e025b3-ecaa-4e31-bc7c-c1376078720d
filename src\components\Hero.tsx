import React, { useEffect, useRef } from "react";
import { ArrowDown } from "lucide-react";

const Hero = () => {
  const typingTextRef = useRef<HTMLSpanElement>(null);
  const texts = ["Web Developer", "UI/UX Designer", "Freelancer"];
  let textIndex = 0;
  let charIndex = 0;
  let isDeleting = false;
  let typingSpeed = 100;

  useEffect(() => {
    function typeText() {
      const currentText = texts[textIndex];

      if (typingTextRef.current) {
        if (isDeleting) {
          typingTextRef.current.textContent = currentText.substring(
            0,
            charIndex - 1
          );
          charIndex--;
          typingSpeed = 50; // Faster when deleting
        } else {
          typingTextRef.current.textContent = currentText.substring(
            0,
            charIndex + 1
          );
          charIndex++;
          typingSpeed = 150; // Normal typing speed
        }

        // If word is complete, pause then start deleting
        if (!isDeleting && charIndex === currentText.length) {
          isDeleting = true;
          typingSpeed = 1000; // Wait before deleting
        }
        // If deletion is complete, move to next word
        else if (isDeleting && charIndex === 0) {
          isDeleting = false;
          textIndex = (textIndex + 1) % texts.length;
          typingSpeed = 500; // Wait before typing new word
        }
      }

      setTimeout(typeText, typingSpeed);
    }

    typeText();
  }, []);

  return (
    <section
      id="home"
      className="min-h-screen flex flex-col justify-center pt-16"
    >
      <div className="container mx-auto px-4">
        <div className="space-y-6">
          <h3 className="text-gray-300 text-lg md:text-xl">
            WELCOME TO MY WORLD
          </h3>
          <h1 className="heading-title">
            Hi, I'm <span className="text-red">John Smith</span>
            <br />a{" "}
            <span ref={typingTextRef} className="typing-text typing-cursor">
              Web Developer
            </span>
          </h1>
          <p className="text-gray-200 max-w-2xl">
            I'm a web designer and developer based in USA. I specialize in
            building exceptional digital experiences. Currently, I'm focused on
            building accessible, human-centered products.
          </p>
          <div className="pt-4">
            <a href="#about" className="btn btn-primary">
              <span>More About Me</span>
            </a>
          </div>
        </div>
      </div>

      <div className="absolute bottom-10 left-0 w-full flex justify-center animate-bounce-slow">
        <a href="#about" className="text-gray-300 hover:text-red">
          <ArrowDown size={30} />
        </a>
      </div>
    </section>
  );
};

export default Hero;
