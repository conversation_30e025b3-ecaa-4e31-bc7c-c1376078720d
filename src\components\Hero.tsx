import React, { useEffect, useRef, useState } from "react";
import { ArrowDown } from "lucide-react";

const Hero = () => {
  const typingTextRef = useRef<HTMLSpanElement>(null);
  const texts = ["Web Developer", "UI/UX Designer", "Freelancer"];
  let textIndex = 0;
  let charIndex = 0;
  let isDeleting = false;
  let typingSpeed = 100;

  // Mobile name and profession typing animation
  const [mobileNameText, setMobileNameText] = useState("");
  const [mobileProfessionText, setMobileProfessionText] = useState("");
  const [showMobileCursor, setShowMobileCursor] = useState(true);

  useEffect(() => {
    function typeText() {
      const currentText = texts[textIndex];

      if (typingTextRef.current) {
        if (isDeleting) {
          typingTextRef.current.textContent = currentText.substring(
            0,
            charIndex - 1
          );
          charIndex--;
          typingSpeed = 50; // Faster when deleting
        } else {
          typingTextRef.current.textContent = currentText.substring(
            0,
            charIndex + 1
          );
          charIndex++;
          typingSpeed = 150; // Normal typing speed
        }

        // If word is complete, pause then start deleting
        if (!isDeleting && charIndex === currentText.length) {
          isDeleting = true;
          typingSpeed = 1000; // Wait before deleting
        }
        // If deletion is complete, move to next word
        else if (isDeleting && charIndex === 0) {
          isDeleting = false;
          textIndex = (textIndex + 1) % texts.length;
          typingSpeed = 500; // Wait before typing new word
        }
      }

      setTimeout(typeText, typingSpeed);
    }

    typeText();
  }, []);

  // Mobile name and profession typing animation
  useEffect(() => {
    const name = "John Smith";
    const profession = "Web Developer";

    // Type name first
    let nameIndex = 0;
    const nameTyping = setInterval(() => {
      if (nameIndex <= name.length) {
        setMobileNameText(name.substring(0, nameIndex));
        nameIndex++;
      } else {
        clearInterval(nameTyping);

        // Start typing profession after name is complete
        setTimeout(() => {
          let professionIndex = 0;
          const professionTyping = setInterval(() => {
            if (professionIndex <= profession.length) {
              setMobileProfessionText(profession.substring(0, professionIndex));
              professionIndex++;
            } else {
              clearInterval(professionTyping);
            }
          }, 80);
        }, 500);
      }
    }, 100);

    // Cursor blinking
    const cursorBlink = setInterval(() => {
      setShowMobileCursor((prev) => !prev);
    }, 500);

    return () => {
      clearInterval(nameTyping);
      clearInterval(cursorBlink);
    };
  }, []);

  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center py-20 lg:py-0"
    >
      <div className="container mx-auto px-4 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8 lg:gap-12">
          {/* Mobile Profile Section - Only visible on mobile */}
          <div className="lg:hidden flex-shrink-0 text-center hero-profile-image">
            {/* Profile Image with enhanced styling */}
            <div className="relative mx-auto w-52 h-52 mb-8">
              <div className="w-full h-full rounded-full overflow-hidden ring-4 ring-red/20 shadow-2xl relative">
                <img
                  src="https://rainbowit.net/html/inbio/assets/images/slider/banner-01.png"
                  alt="John Smith"
                  className="w-full h-full object-cover hover:scale-110 transition-transform duration-500"
                />
                {/* Online status indicator */}
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-dark-bg animate-pulse shadow-lg"></div>
              </div>
              {/* Decorative ring */}
              <div className="absolute inset-0 rounded-full border-2 border-red/10 animate-pulse"></div>
            </div>

            {/* Name and Profession with Typing Animation */}
            <div className="space-y-4 mb-6">
              {/* Name with typing animation */}
              <h1 className="text-2xl md:text-3xl font-bold mobile-name-text">
                {mobileNameText}
                {mobileNameText.length < "John Smith".length &&
                  showMobileCursor && (
                    <span className="text-red animate-pulse">|</span>
                  )}
              </h1>

              {/* Profession with typing animation */}
              <div className="h-8 flex items-center justify-center">
                <p className="text-lg font-medium mobile-profession-text">
                  {mobileProfessionText}
                  {mobileProfessionText.length > 0 &&
                    mobileProfessionText.length < "Web Developer".length &&
                    showMobileCursor && (
                      <span className="animate-pulse">|</span>
                    )}
                </p>
              </div>

              {/* Decorative line */}
              <div className="w-16 h-0.5 bg-gradient-to-r from-red to-red/50 mx-auto"></div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 space-y-6 text-center lg:text-left">
            <h3 className="text-gray-300 text-lg md:text-xl">
              WELCOME TO MY WORLD
            </h3>
            <h1 className="heading-title">
              Hi, I'm <span className="text-red">John Smith</span>
              <br />a{" "}
              <span ref={typingTextRef} className="typing-text typing-cursor">
                Web Developer
              </span>
            </h1>
            <p className="text-gray-200 max-w-2xl mx-auto lg:mx-0">
              I'm a web designer and developer based in USA. I specialize in
              building exceptional digital experiences. Currently, I'm focused
              on building accessible, human-centered products.
            </p>
            <div className="pt-4">
              <a href="#about" className="btn btn-primary">
                <span>More About Me</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div className="absolute bottom-10 left-0 w-full flex justify-center animate-bounce-slow">
        <a href="#about" className="text-gray-300 hover:text-red">
          <ArrowDown size={30} />
        </a>
      </div>
    </section>
  );
};

export default Hero;
