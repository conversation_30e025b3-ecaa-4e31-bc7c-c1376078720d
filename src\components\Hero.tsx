import React, { useEffect, useRef, useState } from "react";
import { ArrowDown } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

const Hero = () => {
  const typingTextRef = useRef<HTMLSpanElement>(null);
  const texts = ["Web Developer", "UI/UX Designer", "Freelancer"];
  let textIndex = 0;
  let charIndex = 0;
  let isDeleting = false;
  let typingSpeed = 100;

  // Mobile name and profession typing animation
  const [mobileNameText, setMobileNameText] = useState("");
  const [mobileProfessionText, setMobileProfessionText] = useState("");
  const [isTyping, setIsTyping] = useState(false);

  const professions = [
    "Web Developer",
    "UI/UX Designer",
    "Freelancer",
    "Creative Designer",
  ];

  useEffect(() => {
    function typeText() {
      const currentText = texts[textIndex];

      if (typingTextRef.current) {
        if (isDeleting) {
          typingTextRef.current.textContent = currentText.substring(
            0,
            charIndex - 1
          );
          charIndex--;
          typingSpeed = 50; // Faster when deleting
        } else {
          typingTextRef.current.textContent = currentText.substring(
            0,
            charIndex + 1
          );
          charIndex++;
          typingSpeed = 150; // Normal typing speed
        }

        // If word is complete, pause then start deleting
        if (!isDeleting && charIndex === currentText.length) {
          isDeleting = true;
          typingSpeed = 1000; // Wait before deleting
        }
        // If deletion is complete, move to next word
        else if (isDeleting && charIndex === 0) {
          isDeleting = false;
          textIndex = (textIndex + 1) % texts.length;
          typingSpeed = 500; // Wait before typing new word
        }
      }

      setTimeout(typeText, typingSpeed);
    }

    typeText();
  }, []);

  // Mobile name and profession typing animation
  useEffect(() => {
    const name = "John Smith";

    // Type name first with realistic keyboard speed
    let nameIndex = 0;
    const typeNameChar = () => {
      if (nameIndex <= name.length) {
        setIsTyping(true);
        setMobileNameText(name.substring(0, nameIndex));
        nameIndex++;

        // Add typing pulse effect
        setTimeout(() => setIsTyping(false), 50);

        // Realistic typing speed with variation (160-240ms)
        setTimeout(typeNameChar, Math.random() * 80 + 160);
      } else {
        setIsTyping(false);
        // Start profession typing loop after name is complete
        setTimeout(() => {
          startProfessionTyping();
        }, 800);
      }
    };

    typeNameChar();

    return () => {
      // Cleanup handled by setTimeout
    };
  }, []);

  // Continuous profession typing animation - realistic keyboard typing
  const startProfessionTyping = () => {
    let professionIndex = 0;
    let isDeleting = false;
    let currentIndex = 0;

    const getRandomTypingSpeed = (isDeleting = false) => {
      if (isDeleting) {
        return Math.random() * 60 + 80; // 80-140ms for deleting (slower, more visible)
      }
      return Math.random() * 80 + 140; // 140-220ms for typing (realistic keyboard speed)
    };

    const typeProfession = () => {
      const currentProfession = professions[currentIndex];

      if (!isDeleting) {
        // Typing forward with realistic speed variation
        if (professionIndex <= currentProfession.length) {
          setIsTyping(true);
          setMobileProfessionText(
            currentProfession.substring(0, professionIndex)
          );
          professionIndex++;

          // Add typing pulse effect
          setTimeout(() => setIsTyping(false), 50);

          setTimeout(typeProfession, getRandomTypingSpeed());
        } else {
          setIsTyping(false);
          // Longer pause before deleting (3.5 seconds to read)
          setTimeout(() => {
            isDeleting = true;
            typeProfession();
          }, 3500);
        }
      } else {
        // Deleting backward with realistic speed variation
        if (professionIndex > 0) {
          setIsTyping(true);
          setMobileProfessionText(
            currentProfession.substring(0, professionIndex - 1)
          );
          professionIndex--;

          // Add deletion pulse effect
          setTimeout(() => setIsTyping(false), 30);

          setTimeout(typeProfession, getRandomTypingSpeed(true));
        } else {
          setIsTyping(false);
          // Move to next profession with pause
          isDeleting = false;
          currentIndex = (currentIndex + 1) % professions.length;
          setTimeout(typeProfession, 800);
        }
      }
    };

    typeProfession();
  };

  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center py-20 lg:py-0"
    >
      <div className="container mx-auto px-4 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8 lg:gap-12">
          {/* Mobile Profile Section - Only visible on mobile */}
          <div className="lg:hidden flex-shrink-0 text-center hero-profile-image">
            {/* Profile Image with enhanced styling */}
            <div className="relative mx-auto w-52 h-52 mb-8">
              <div className="relative">
                <Avatar className="w-full h-full ring-4 ring-red/20 shadow-2xl hover:scale-110 transition-transform duration-500">
                  <AvatarImage
                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face&auto=format&q=80"
                    alt="John Smith"
                    className="object-cover"
                  />
                  <AvatarFallback className="bg-red/20 text-red text-4xl font-bold">
                    JS
                  </AvatarFallback>
                </Avatar>
                {/* Online status indicator */}
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-dark-bg animate-pulse shadow-lg"></div>
              </div>
              {/* Decorative ring */}
              <div className="absolute inset-0 rounded-full border-2 border-red/10 animate-pulse"></div>
            </div>

            {/* Name and Profession with Typing Animation */}
            <div className="space-y-4 mb-6">
              {/* Name with typing animation */}
              <h1 className="text-2xl md:text-3xl font-bold mobile-name-text">
                {mobileNameText}
                {mobileNameText.length < "John Smith".length && (
                  <span
                    className={`typing-cursor ${
                      isTyping ? "typing-active" : ""
                    }`}
                  ></span>
                )}
              </h1>

              {/* Profession with typing animation */}
              <div className="h-8 flex items-center justify-center">
                <p className="text-lg font-medium mobile-profession-text">
                  {mobileProfessionText}
                  <span
                    className={`typing-cursor ${
                      isTyping ? "typing-active" : ""
                    }`}
                  ></span>
                </p>
              </div>

              {/* Decorative line */}
              <div className="w-16 h-0.5 bg-gradient-to-r from-red to-red/50 mx-auto"></div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 space-y-6 text-center lg:text-left">
            <h3 className="text-gray-300 text-lg md:text-xl">
              WELCOME TO MY WORLD
            </h3>
            <h1 className="heading-title">
              Hi, I'm <span className="text-red">John Smith</span>
              <br />a{" "}
              <span ref={typingTextRef} className="typing-text typing-cursor">
                Web Developer
              </span>
            </h1>
            <p className="text-gray-200 max-w-2xl mx-auto lg:mx-0">
              I'm a web designer and developer based in USA. I specialize in
              building exceptional digital experiences. Currently, I'm focused
              on building accessible, human-centered products.
            </p>
            <div className="pt-4">
              <a href="#about" className="btn btn-primary">
                <span>More About Me</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div className="absolute bottom-10 left-0 w-full flex justify-center animate-bounce-slow">
        <a href="#about" className="text-gray-300 hover:text-red">
          <ArrowDown size={30} />
        </a>
      </div>
    </section>
  );
};

export default Hero;
