import React, { useEffect, useRef, useState } from "react";
import { ArrowDown } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

const Hero = () => {
  const typingTextRef = useRef<HTMLSpanElement>(null);
  const texts = ["Web Developer", "UI/UX Designer", "Freelancer"];
  let textIndex = 0;
  let charIndex = 0;
  let isDeleting = false;
  let typingSpeed = 100;

  // Simple state for mobile name and profession
  const [mobileNameText, setMobileNameText] = useState("");
  const [showNameCursor, setShowNameCursor] = useState(true);
  const [startProfessionAnimation, setStartProfessionAnimation] =
    useState(false);
  const [currentProfession, setCurrentProfession] = useState("Web Developer");

  const professions = [
    "Web Developer",
    "UI/UX Designer",
    "Freelancer",
    "Creative Designer",
  ];

  useEffect(() => {
    function typeText() {
      const currentText = texts[textIndex];

      if (typingTextRef.current) {
        if (isDeleting) {
          typingTextRef.current.textContent = currentText.substring(
            0,
            charIndex - 1
          );
          charIndex--;
          typingSpeed = 50; // Faster when deleting
        } else {
          typingTextRef.current.textContent = currentText.substring(
            0,
            charIndex + 1
          );
          charIndex++;
          typingSpeed = 150; // Normal typing speed
        }

        // If word is complete, pause then start deleting
        if (!isDeleting && charIndex === currentText.length) {
          isDeleting = true;
          typingSpeed = 1000; // Wait before deleting
        }
        // If deletion is complete, move to next word
        else if (isDeleting && charIndex === 0) {
          isDeleting = false;
          textIndex = (textIndex + 1) % texts.length;
          typingSpeed = 500; // Wait before typing new word
        }
      }

      setTimeout(typeText, typingSpeed);
    }

    typeText();
  }, []);

  // Enhanced mobile name typing animation with cursor control
  useEffect(() => {
    const name = "John Smith";
    let nameIndex = 0;

    const typeNameChar = () => {
      if (nameIndex <= name.length) {
        setMobileNameText(name.substring(0, nameIndex));
        nameIndex++;
        setTimeout(typeNameChar, 150);
      } else {
        // Name typing complete - hide cursor and start profession animation
        setTimeout(() => {
          setShowNameCursor(false);
          setTimeout(() => {
            setStartProfessionAnimation(true);
          }, 300);
        }, 500);
      }
    };

    typeNameChar();
  }, []);

  // Cycle through professions after animation starts
  useEffect(() => {
    if (startProfessionAnimation) {
      let professionIndex = 0;

      const cycleProfessions = () => {
        setCurrentProfession(professions[professionIndex]);
        professionIndex = (professionIndex + 1) % professions.length;
      };

      // Start immediately with first profession
      cycleProfessions();

      // Then cycle every 4 seconds
      const interval = setInterval(cycleProfessions, 4000);

      return () => clearInterval(interval);
    }
  }, [startProfessionAnimation]);

  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center py-20 lg:py-0"
    >
      <div className="container mx-auto px-4 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8 lg:gap-12">
          {/* Mobile Profile Section - Only visible on mobile */}
          <div className="lg:hidden flex-shrink-0 text-center hero-profile-image">
            {/* Profile Image with enhanced styling */}
            <div className="relative mx-auto w-36 h-36 mb-5 mt-20">
              <div className="relative">
                <Avatar className="w-full h-full ring-3 ring-red/20 shadow-xl hover:scale-110 transition-transform duration-500">
                  <AvatarImage
                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face&auto=format&q=80"
                    alt="John Smith"
                    className="object-cover"
                  />
                  <AvatarFallback className="bg-red/20 text-red text-3xl font-bold">
                    JS
                  </AvatarFallback>
                </Avatar>
                {/* Online status indicator */}
                <div className="absolute bottom-1 right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-dark-bg animate-pulse shadow-md"></div>
              </div>
              {/* Decorative ring */}
              <div className="absolute inset-0 rounded-full border-2 border-red/10 animate-pulse"></div>
            </div>

            {/* Name and Profession with Enhanced Cursors */}
            <div className="space-y-2 mb-3">
              {/* Name with enhanced colored cursor */}
              <h1 className="text-xl md:text-2xl font-bold mobile-name-text leading-tight">
                {mobileNameText}
                {showNameCursor && (
                  <span className="enhanced-name-cursor"></span>
                )}
              </h1>

              {/* Profession with smooth CSS animation - starts after name */}
              <div className="h-6 flex items-center justify-center mb-1">
                {startProfessionAnimation && (
                  <div
                    key={currentProfession}
                    className="typing-text text-base md:text-lg font-medium mobile-profession-text"
                  >
                    <span>{currentProfession}</span>
                  </div>
                )}
              </div>

              {/* Decorative line */}
              <div className="w-14 h-0.5 bg-gradient-to-r from-red to-red/50 mx-auto mt-1"></div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 space-y-6 text-center lg:text-left">
            <h3 className="text-gray-300 text-lg md:text-xl">
              WELCOME TO MY WORLD
            </h3>
            <h1 className="heading-title">
              Hi, I'm <span className="text-red">John Smith</span>
              <br />a{" "}
              <span ref={typingTextRef} className="typing-text typing-cursor">
                Web Developer
              </span>
            </h1>
            <p className="text-gray-200 max-w-2xl mx-auto lg:mx-0">
              I'm a web designer and developer based in USA. I specialize in
              building exceptional digital experiences. Currently, I'm focused
              on building accessible, human-centered products.
            </p>
            <div className="pt-4">
              <a href="#about" className="btn btn-primary">
                <span>More About Me</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div className="absolute bottom-10 left-0 w-full flex justify-center animate-bounce-slow">
        <a href="#about" className="text-gray-300 hover:text-red">
          <ArrowDown size={30} />
        </a>
      </div>
    </section>
  );
};

export default Hero;
