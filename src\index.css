@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Screen reader only class for accessibility */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 346.8 77.2% 49.8%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 346.8 77.2% 49.8%;
    --radius: 0.75rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 346.8 77.2% 49.8%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 346.8 77.2% 49.8%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-dark-bg text-foreground font-poppins overflow-x-hidden;
  }

  ::selection {
    @apply bg-red text-white;
  }
}

@layer components {
  .heading-title {
    @apply text-gray-300 text-4xl md:text-5xl lg:text-6xl font-bold mb-6;
  }

  .section-title {
    @apply text-xl font-medium relative inline-block pb-2 text-gray-300 before:absolute before:w-2/3 before:h-[2px] before:bg-red before:bottom-0 before:left-0;
  }

  .section {
    @apply py-16 md:py-20 px-4 md:px-8;
  }

  .card {
    @apply bg-dark-lighter rounded-xl p-6 transition-all duration-300 border border-transparent hover:border-red hover:shadow-lg hover:shadow-red/10;
  }

  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300 disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-red hover:bg-red-light text-white py-3 px-8;
  }

  .btn-outline {
    @apply border border-gray-300/40 hover:border-red text-gray-300 hover:text-red py-3 px-8;
  }

  .nav-link {
    @apply block py-3 px-6 text-gray-300 hover:text-white hover:bg-red rounded-lg transition-all duration-300;
  }

  .nav-link-dash {
    @apply block py-3 px-6 hover:text-red rounded-lg transition-all duration-300 relative;
  }

  .active-nav-link {
    @apply bg-red text-white;
  }

  .progress-bar {
    @apply h-1 bg-red-light rounded-full;
  }

  .social-icon {
    @apply w-12 h-12 flex items-center justify-center rounded-lg bg-dark-lighter text-gray-300 hover:text-red hover:bg-transparent border border-transparent hover:border-red transition-all duration-300;
  }

  .portfolio-filter-btn {
    @apply btn py-2 px-4 text-gray-300 hover:text-white bg-dark-lighter hover:bg-red rounded-lg;
  }

  /* Enhanced Portfolio Styles */
  .enhanced-portfolio-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    background: linear-gradient(135deg, #ffffff 0%, #ff014f 50%, #ffffff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    position: relative;
    animation: portfolioTitleGlow 3s ease-in-out infinite alternate;
  }

  .portfolio-title-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(
      ellipse,
      rgba(255, 1, 79, 0.15) 0%,
      transparent 70%
    );
    border-radius: 50%;
    animation: portfolioTitlePulse 4s ease-in-out infinite alternate;
    z-index: -1;
  }

  .portfolio-title-underline {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, transparent, #ff014f, transparent);
    border-radius: 2px;
    animation: portfolioUnderlineGlow 2s ease-in-out infinite alternate;
  }

  .portfolio-description-container {
    margin-top: 2rem;
    position: relative;
  }

  .portfolio-description {
    font-size: 1.125rem;
    line-height: 1.8;
    color: #a1a1aa;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
  }

  /* Mobile Portfolio Section Adjustments */
  @media (max-width: 768px) {
    .enhanced-portfolio-title {
      font-size: clamp(2rem, 8vw, 3rem);
      margin-bottom: 1rem;
    }

    .portfolio-description {
      font-size: 1rem;
      line-height: 1.6;
      padding: 0 1rem;
    }

    .portfolio-description-container {
      margin-top: 1.5rem;
    }
  }

  @media (max-width: 480px) {
    .enhanced-portfolio-title {
      font-size: clamp(1.75rem, 10vw, 2.5rem);
    }

    .portfolio-description {
      font-size: 0.9rem;
      padding: 0 0.5rem;
    }
  }

  /* Enhanced Filter Buttons */
  .enhanced-filter-btn {
    position: relative;
    padding: 12px 24px;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 1, 79, 0.05) 100%
    );
    border: 1px solid rgba(255, 1, 79, 0.2);
    border-radius: 50px;
    color: #a1a1aa;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    cursor: pointer;
    animation: filterBtnFadeIn 0.8s ease-out;
  }

  .enhanced-filter-btn:hover {
    transform: translateY(-2px) scale(1.05);
    border-color: rgba(255, 1, 79, 0.5);
    color: #ffffff;
    box-shadow: 0 10px 30px rgba(255, 1, 79, 0.2);
  }

  .enhanced-filter-btn.active {
    background: linear-gradient(135deg, #ff014f 0%, #ff4081 100%);
    border-color: #ff014f;
    color: #ffffff;
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 1, 79, 0.3);
  }

  .filter-btn-text {
    position: relative;
    z-index: 2;
  }

  .filter-btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
      circle,
      rgba(255, 1, 79, 0.3) 0%,
      transparent 70%
    );
    border-radius: 50px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .enhanced-filter-btn:hover .filter-btn-glow,
  .enhanced-filter-btn.active .filter-btn-glow {
    opacity: 1;
  }

  .filter-btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
    border-radius: 50px;
  }

  .filter-btn-particles::before,
  .filter-btn-particles::after {
    content: "";
    position: absolute;
    width: 2px;
    height: 2px;
    background: #ff014f;
    border-radius: 50%;
    opacity: 0;
    animation: filterParticleFloat 3s ease-in-out infinite;
  }

  .enhanced-filter-btn:hover .filter-btn-particles::before,
  .enhanced-filter-btn.active .filter-btn-particles::before {
    opacity: 1;
    top: 20%;
    left: 20%;
    animation-delay: 0s;
  }

  .enhanced-filter-btn:hover .filter-btn-particles::after,
  .enhanced-filter-btn.active .filter-btn-particles::after {
    opacity: 1;
    top: 60%;
    right: 20%;
    animation-delay: 1s;
  }

  /* Mobile Filter Button Adjustments */
  @media (max-width: 768px) {
    .enhanced-filter-btn {
      padding: 10px 20px;
      font-size: 0.85rem;
      margin: 0.25rem;
    }
  }

  @media (max-width: 480px) {
    .enhanced-filter-btn {
      padding: 8px 16px;
      font-size: 0.8rem;
      margin: 0.2rem;
    }
  }

  /* Portfolio Grid */
  .portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    transition: all 0.5s ease;
    justify-items: center;
    width: 100%;
  }

  /* Mobile Portfolio Grid Adjustments */
  @media (max-width: 768px) {
    .portfolio-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      padding: 0 1rem;
      justify-items: center;
      align-items: center;
    }
  }

  @media (max-width: 480px) {
    .portfolio-grid {
      grid-template-columns: 1fr;
      gap: 1.25rem;
      padding: 0 0.5rem;
      justify-items: center;
    }
  }

  .portfolio-grid.animating {
    opacity: 0.7;
    transform: scale(0.98);
  }

  .enhanced-portfolio-item {
    animation: portfolioItemFadeIn 0.8s ease-out;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }

  .portfolio-card {
    position: relative;
    height: 100%;
    width: 100%;
    border-radius: 20px;
    overflow: hidden;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.03) 0%,
      rgba(255, 1, 79, 0.02) 100%
    );
    border: 1px solid rgba(255, 1, 79, 0.1);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  /* Mobile Portfolio Card Adjustments */
  @media (max-width: 768px) {
    .enhanced-portfolio-item {
      max-width: 100%;
      width: 100%;
    }

    .portfolio-card {
      max-width: 350px;
      margin: 0 auto;
    }
  }

  @media (max-width: 480px) {
    .enhanced-portfolio-item {
      max-width: 100%;
      width: 100%;
    }

    .portfolio-card {
      max-width: 320px;
      margin: 0 auto;
    }

    .portfolio-image-container {
      height: 240px;
    }
  }

  .portfolio-card:hover {
    transform: translateY(-10px) scale(1.02);
    border-color: rgba(255, 1, 79, 0.3);
    box-shadow: 0 25px 50px rgba(255, 1, 79, 0.15);
  }

  .portfolio-image-container {
    position: relative;
    width: 100%;
    height: 280px;
    overflow: hidden;
  }

  .portfolio-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .portfolio-card:hover .portfolio-image {
    transform: scale(1.1);
    filter: brightness(0.7);
  }

  .portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(255, 1, 79, 0.3) 100%
    );
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  .portfolio-card:hover .portfolio-overlay {
    opacity: 1;
  }

  .portfolio-content {
    text-align: center;
    transform: translateY(30px);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .portfolio-card:hover .portfolio-content {
    transform: translateY(0);
  }

  .portfolio-year {
    display: inline-block;
    padding: 4px 12px;
    background: rgba(255, 1, 79, 0.2);
    border: 1px solid rgba(255, 1, 79, 0.3);
    border-radius: 20px;
    color: #ff014f;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 1rem;
    backdrop-filter: blur(10px);
  }

  .portfolio-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.75rem;
    line-height: 1.3;
  }

  .portfolio-description {
    font-size: 0.9rem;
    color: #a1a1aa;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }

  .portfolio-technologies {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1rem;
  }

  .tech-badge {
    padding: 4px 10px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    color: #ffffff;
    font-size: 0.7rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .tech-badge:hover {
    background: rgba(255, 1, 79, 0.2);
    border-color: rgba(255, 1, 79, 0.4);
    transform: scale(1.05);
  }

  .portfolio-category {
    color: #ff014f;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  /* Mobile Portfolio Content Adjustments */
  @media (max-width: 768px) {
    .portfolio-overlay {
      padding: 1.5rem;
    }

    .portfolio-title {
      font-size: 1.25rem;
      margin-bottom: 0.5rem;
    }

    .portfolio-description {
      font-size: 0.85rem;
      margin-bottom: 1rem;
    }

    .tech-badge {
      font-size: 0.65rem;
      padding: 3px 8px;
    }

    .portfolio-category {
      font-size: 0.8rem;
    }
  }

  @media (max-width: 480px) {
    .portfolio-overlay {
      padding: 1rem;
    }

    .portfolio-title {
      font-size: 1.1rem;
    }

    .portfolio-description {
      font-size: 0.8rem;
      margin-bottom: 0.75rem;
    }

    .portfolio-technologies {
      gap: 0.25rem;
      margin-bottom: 0.75rem;
    }

    .tech-badge {
      font-size: 0.6rem;
      padding: 2px 6px;
    }
  }

  .typing-text {
    @apply overflow-hidden border-r-4 border-red whitespace-nowrap animate-typing;
  }

  .typing-cursor {
    @apply animate-blink-caret;
  }
}

/* Animated navbar dash underline */
@keyframes navDash {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

.animate-nav-dash {
  animation: navDash 0.5s forwards;
}

/* Mobile menu slide animations */
@keyframes slide-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out-left {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* Mobile menu item animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Glassmorphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Mobile menu animations */
.mobile-nav-item {
  animation: slideInUp 0.3s ease-out forwards;
}

/* Navigation item click feedback */
.nav-item-clicked {
  transform: scale(0.95);
  transition: transform 0.1s ease-out;
}

.mobile-nav-item:nth-child(1) {
  animation-delay: 0.1s;
}
.mobile-nav-item:nth-child(2) {
  animation-delay: 0.15s;
}
.mobile-nav-item:nth-child(3) {
  animation-delay: 0.2s;
}
.mobile-nav-item:nth-child(4) {
  animation-delay: 0.25s;
}
.mobile-nav-item:nth-child(5) {
  animation-delay: 0.3s;
}
.mobile-nav-item:nth-child(6) {
  animation-delay: 0.35s;
}

/* Hamburger menu animation improvements */
.hamburger-line {
  transform-origin: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced mobile menu backdrop */
.mobile-menu-backdrop {
  backdrop-filter: blur(8px);
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.6) 100%
  );
}

/* Mobile menu content slide animation */
.mobile-menu-content {
  animation: slideInFromLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Social links hover effect */
.social-link {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.social-link:hover {
  transform: translateY(-2px) scale(1.1);
  box-shadow: 0 8px 25px rgba(255, 1, 79, 0.3);
}

/* Active navigation item glow effect */
.nav-item-active {
  box-shadow: 0 0 20px rgba(255, 1, 79, 0.3), 0 0 40px rgba(255, 1, 79, 0.1);
}

/* Mobile menu profile section animation */
.profile-section {
  animation: fadeInScale 0.6s ease-out 0.2s both;
}

/* Enhanced mobile responsiveness */
@media (max-width: 320px) {
  .mobile-menu-content {
    width: 100vw;
  }
}

/* Enhanced mobile menu scrollbar styling */
.mobile-menu-scrollable::-webkit-scrollbar {
  width: 2px;
}

.mobile-menu-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-menu-scrollable::-webkit-scrollbar-thumb {
  background: rgba(255, 1, 79, 0.2);
  border-radius: 1px;
}

.mobile-menu-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 1, 79, 0.4);
}

/* Hide scrollbar on mobile devices but keep functionality */
@media (max-width: 768px) {
  .mobile-menu-scrollable {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .mobile-menu-scrollable::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

/* Remove default drawer backgrounds */
[data-vaul-drawer] {
  background: transparent !important;
}

[data-vaul-overlay] {
  background: transparent !important;
}

/* Ensure full height coverage */
.mobile-menu-full-height {
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height for mobile */
  min-height: 100vh;
  min-height: 100dvh;
}

/* Prevent body scroll when mobile menu is open */
.mobile-menu-open {
  overflow: hidden;
}

/* Enhanced mobile menu positioning */
.mobile-menu-content {
  top: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  right: auto !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

/* Override default drawer styles */
.mobile-menu-content > div:first-child {
  display: none !important; /* Hide the drag handle */
}

/* Animated skill progress bars */
@keyframes progressWidth {
  0% {
    width: 0;
  }
  100% {
    width: var(--percentage);
  }
}

@keyframes countUp {
  0% {
    content: "0%";
  }
  100% {
    content: attr(data-percentage) "%";
  }
}

.animate-progress {
  animation: progressWidth 1.5s forwards;
}

.skill-item.animated .progress-bar {
  animation: progressWidth 1.5s forwards;
}

/* Counter animation for percentage */
.skill-item.animated .counter-number {
  animation: countNumber 1.5s forwards;
}

@keyframes countNumber {
  0% {
    content: "0%";
  }
  100% {
    content: attr(data-percentage) "%";
  }
}

/* Scroll bar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e2024;
}

::-webkit-scrollbar-thumb {
  background: #c4cfde;
  border-radius: 50px;
}

::-webkit-scrollbar-thumb:hover {
  background: #ff014f;
}

/* Perfect mobile hero centering */
@media (max-width: 1024px) {
  #home {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
  }
}

@media (max-width: 768px) {
  #home {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .hero-profile-image {
    margin-bottom: 1rem;
  }
}

/* Perfect mobile centering for smaller screens */
@media (max-width: 640px) {
  #home {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }

  .hero-profile-image .mx-auto {
    margin-bottom: 1.5rem;
  }
}

/* Enhanced mobile profile styling */
.hero-profile-image .relative::before {
  content: "";
  position: absolute;
  inset: -10px;
  border-radius: 50%;
  background: linear-gradient(45deg, #ff014f, transparent, #ff014f);
  opacity: 0.2;
  animation: rotate 4s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Mobile typing animation enhancements */
.mobile-name-text {
  background: linear-gradient(135deg, #ffffff, #e5e5e5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.mobile-profession-text {
  background: linear-gradient(135deg, #ff014f, #ff6b9d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(255, 1, 79, 0.3);
}

/* Modern Enhanced Typing Cursor */
.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 1.1em;
  margin-left: 2px;
  position: relative;
  animation: modernBlink 1s infinite ease-in-out;
}

.typing-cursor::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #ffffff 0%, #e5e5e5 50%, #ffffff 100%);
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(255, 255, 255, 0.4), 0 0 30px rgba(255, 255, 255, 0.2);
  animation: cursorGlow 2s infinite alternate ease-in-out;
}

.typing-cursor::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -1px;
  width: 4px;
  height: calc(100% + 4px);
  background: radial-gradient(
    ellipse at center,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: cursorAura 1.5s infinite ease-in-out;
}

@keyframes modernBlink {
  0%,
  50% {
    opacity: 1;
    transform: scaleY(1);
  }
  51%,
  100% {
    opacity: 0;
    transform: scaleY(0.8);
  }
}

@keyframes cursorGlow {
  0% {
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8),
      0 0 20px rgba(255, 255, 255, 0.4), 0 0 30px rgba(255, 255, 255, 0.2);
    filter: brightness(1);
  }
  100% {
    box-shadow: 0 0 15px rgba(255, 255, 255, 1),
      0 0 25px rgba(255, 255, 255, 0.6), 0 0 35px rgba(255, 255, 255, 0.3);
    filter: brightness(1.2);
  }
}

@keyframes cursorAura {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

/* Enhanced cursor for name text */
.mobile-name-text .typing-cursor::before {
  background: linear-gradient(180deg, #ffffff 0%, #f0f0f0 50%, #ffffff 100%);
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.9),
    0 0 24px rgba(255, 255, 255, 0.5), 0 0 36px rgba(255, 255, 255, 0.2),
    inset 0 0 3px rgba(255, 255, 255, 0.8);
}

/* Enhanced cursor for profession text */
.mobile-profession-text .typing-cursor::before {
  background: linear-gradient(
    180deg,
    #ffffff 0%,
    #ffebf0 30%,
    #ffffff 70%,
    #f0f0f0 100%
  );
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.9), 0 0 24px rgba(255, 1, 79, 0.3),
    0 0 36px rgba(255, 255, 255, 0.2), inset 0 0 3px rgba(255, 255, 255, 0.8);
}

/* Enhanced Name Cursor */
.enhanced-name-cursor {
  display: inline-block;
  width: 3px;
  height: 1.1em;
  margin-left: 3px;
  position: relative;
  animation: ultraBlink 1.2s infinite ease-in-out;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
}

.enhanced-name-cursor::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    #ffffff 0%,
    #f8f9fa 25%,
    #e9ecef 50%,
    #f8f9fa 75%,
    #ffffff 100%
  );
  border-radius: 3px;
  box-shadow: 0 0 8px rgba(255, 255, 255, 1), 0 0 16px rgba(255, 255, 255, 0.8),
    0 0 24px rgba(255, 255, 255, 0.6), 0 0 32px rgba(255, 255, 255, 0.4),
    inset 0 0 4px rgba(255, 255, 255, 0.9), inset 0 1px 0 rgba(255, 255, 255, 1);
  animation: ultraGlow 2.5s infinite alternate ease-in-out;
}

.enhanced-name-cursor::after {
  content: "";
  position: absolute;
  top: -3px;
  left: -2px;
  width: 7px;
  height: calc(100% + 6px);
  background: radial-gradient(
    ellipse at center,
    rgba(255, 255, 255, 0.5) 0%,
    rgba(255, 255, 255, 0.3) 30%,
    rgba(255, 255, 255, 0.1) 60%,
    transparent 80%
  );
  border-radius: 50%;
  animation: ultraAura 2s infinite ease-in-out;
}

@keyframes ultraBlink {
  0%,
  45% {
    opacity: 1;
    transform: scaleY(1) scaleX(1);
  }
  46%,
  100% {
    opacity: 0;
    transform: scaleY(0.9) scaleX(1.1);
  }
}

@keyframes ultraGlow {
  0% {
    box-shadow: 0 0 8px rgba(255, 255, 255, 1),
      0 0 16px rgba(255, 255, 255, 0.8), 0 0 24px rgba(255, 255, 255, 0.6),
      0 0 32px rgba(255, 255, 255, 0.4), inset 0 0 4px rgba(255, 255, 255, 0.9),
      inset 0 1px 0 rgba(255, 255, 255, 1);
    filter: brightness(1) saturate(1);
  }
  100% {
    box-shadow: 0 0 12px rgba(255, 255, 255, 1),
      0 0 20px rgba(255, 255, 255, 0.9), 0 0 28px rgba(255, 255, 255, 0.7),
      0 0 36px rgba(255, 255, 255, 0.5), inset 0 0 6px rgba(255, 255, 255, 1),
      inset 0 2px 0 rgba(255, 255, 255, 1);
    filter: brightness(1.4) saturate(1.1);
  }
}

@keyframes ultraAura {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1) rotate(0deg);
  }
  25% {
    opacity: 0.6;
    transform: scale(1.2) rotate(90deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.4) rotate(180deg);
  }
  75% {
    opacity: 0.6;
    transform: scale(1.2) rotate(270deg);
  }
}

/* Enhanced Hero Content Styling */

/* Welcome Text Enhancement */
.welcome-text {
  background: linear-gradient(135deg, #c4cfde 0%, #ffffff 50%, #c4cfde 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(196, 207, 222, 0.3);
  animation: welcomeGlow 3s ease-in-out infinite alternate;
  position: relative;
}

.welcome-underline {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #ff014f, transparent);
  animation: underlineExpand 2s ease-in-out infinite alternate;
}

@media (min-width: 1024px) {
  .welcome-underline {
    left: 0;
    transform: translateX(0);
  }
}

/* Enhanced Main Heading */
.enhanced-heading {
  position: relative;
  z-index: 2;
}

.hero-greeting {
  color: #c4cfde;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-name {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(255, 1, 79, 0.4);
  animation: nameGlowPulse 2s ease-in-out infinite alternate;
  position: relative;
}

.hero-article {
  color: #c4cfde;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-profession {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInUp 1s ease-out 0.6s both;
}

/* Hero Glow Effect */
.hero-glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  background: radial-gradient(
    circle,
    rgba(255, 1, 79, 0.1) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: heroGlow 4s ease-in-out infinite alternate;
  z-index: -1;
}

/* Hero Particles */
.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.hero-particles::before,
.hero-particles::after {
  content: "";
  position: absolute;
  width: 4px;
  height: 4px;
  background: #ff014f;
  border-radius: 50%;
  animation: particleFloat 6s ease-in-out infinite;
}

.hero-particles::before {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.hero-particles::after {
  top: 60%;
  right: 15%;
  animation-delay: 3s;
}

/* Typing animation enhancement */
.typing-cursor.typing-active {
  animation: typingPulse 0.1s ease-out;
}

/* Enhanced Description */
.hero-description {
  animation: fadeInUp 1s ease-out 0.8s both;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.highlight-text {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  position: relative;
}

.highlight-text::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ff014f, transparent);
  animation: highlightGlow 2s ease-in-out infinite alternate;
}

/* Enhanced Button */
.enhanced-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16px 32px;
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  border-radius: 50px;
  text-decoration: none;
  color: white;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.5px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(255, 1, 79, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: fadeInUp 1s ease-out 1s both;
}

.enhanced-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 1, 79, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.btn-text {
  position: relative;
  z-index: 2;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%
  );
  border-radius: 50px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-btn:hover .btn-glow {
  opacity: 1;
}

.btn-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border-radius: 50px;
  overflow: hidden;
}

.btn-particles::before,
.btn-particles::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: btnParticleFloat 3s ease-in-out infinite;
}

.btn-particles::before {
  top: 30%;
  left: 20%;
  animation-delay: 0s;
}

.btn-particles::after {
  top: 70%;
  right: 25%;
  animation-delay: 1.5s;
}

/* Enhanced Hero Keyframe Animations */
@keyframes welcomeGlow {
  0% {
    text-shadow: 0 0 30px rgba(196, 207, 222, 0.3);
    filter: brightness(1);
  }
  100% {
    text-shadow: 0 0 40px rgba(196, 207, 222, 0.5);
    filter: brightness(1.2);
  }
}

@keyframes underlineExpand {
  0% {
    width: 40px;
    opacity: 0.6;
  }
  100% {
    width: 80px;
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes nameGlowPulse {
  0% {
    text-shadow: 0 0 40px rgba(255, 1, 79, 0.4);
    filter: brightness(1);
  }
  100% {
    text-shadow: 0 0 60px rgba(255, 1, 79, 0.6);
    filter: brightness(1.3);
  }
}

@keyframes heroGlow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-30px) rotate(270deg);
    opacity: 0.6;
  }
}

@keyframes highlightGlow {
  0% {
    opacity: 0.6;
    transform: scaleX(0.8);
  }
  100% {
    opacity: 1;
    transform: scaleX(1.2);
  }
}

@keyframes btnParticleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px) scale(1.5);
    opacity: 0.8;
  }
}

/* Enhanced About Section Styling */

/* Background Effects */
.about-bg-glow {
  position: absolute;
  top: 20%;
  left: 10%;
  width: 400px;
  height: 400px;
  background: radial-gradient(
    circle,
    rgba(255, 1, 79, 0.08) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: aboutBgFloat 8s ease-in-out infinite alternate;
  z-index: 1;
}

.about-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.about-particles::before,
.about-particles::after {
  content: "";
  position: absolute;
  width: 3px;
  height: 3px;
  background: #ff014f;
  border-radius: 50%;
  animation: aboutParticleFloat 10s ease-in-out infinite;
}

.about-particles::before {
  top: 30%;
  left: 20%;
  animation-delay: 0s;
}

.about-particles::after {
  top: 70%;
  right: 25%;
  animation-delay: 5s;
}

/* Enhanced Section Title */
.enhanced-section-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #c4cfde 50%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: titleFadeIn 1s ease-out;
}

.title-glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 80px;
  background: radial-gradient(
    ellipse,
    rgba(255, 1, 79, 0.15) 0%,
    transparent 70%
  );
  animation: titleGlow 3s ease-in-out infinite alternate;
  z-index: -1;
}

.title-underline-animated {
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #ff014f 0%, #ff6b9d 100%);
  border-radius: 2px;
  animation: underlineSlide 2s ease-out;
}

/* Enhanced Text Blocks */
.about-text-block {
  position: relative;
  padding: 20px 0;
  animation: textBlockFadeIn 1s ease-out;
}

.about-text-block:nth-child(1) {
  animation-delay: 0.3s;
}

.about-text-block:nth-child(2) {
  animation-delay: 0.6s;
}

.about-description {
  color: #c4cfde;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.8;
}

.highlight-keyword {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  position: relative;
}

.highlight-keyword::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ff014f, transparent);
  animation: keywordGlow 2s ease-in-out infinite alternate;
}

.highlight-number {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff014f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  font-size: 1.1em;
}

.tech-highlight {
  background: linear-gradient(135deg, #c4cfde 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  padding: 2px 4px;
  border-radius: 4px;
  position: relative;
}

.tech-highlight::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 1, 79, 0.1);
  border-radius: 4px;
  z-index: -1;
}

/* Enhanced Info Items */
.info-item {
  position: relative;
  padding: 16px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 1, 79, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: infoItemFadeIn 1s ease-out;
  overflow: hidden;
}

.info-item:nth-child(1) {
  animation-delay: 0.9s;
}
.info-item:nth-child(2) {
  animation-delay: 1.1s;
}
.info-item:nth-child(3) {
  animation-delay: 1.3s;
}
.info-item:nth-child(4) {
  animation-delay: 1.5s;
}

.info-item:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 1, 79, 0.3);
  box-shadow: 0 8px 25px rgba(255, 1, 79, 0.15);
}

.info-label {
  color: #ff014f;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.info-value {
  color: #ffffff;
  font-weight: 500;
  font-size: 1rem;
}

.info-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 1, 79, 0.05) 0%,
    transparent 50%
  );
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.info-item:hover .info-glow {
  opacity: 1;
}

/* Portfolio Background Effects */
.portfolio-bg-glow {
  position: absolute;
  top: 20%;
  left: 50%;
  transform: translateX(-50%);
  width: 600px;
  height: 600px;
  background: radial-gradient(
    circle,
    rgba(255, 1, 79, 0.08) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: portfolioBgGlow 8s ease-in-out infinite alternate;
  z-index: -1;
}

.portfolio-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.portfolio-particles::before,
.portfolio-particles::after {
  content: "";
  position: absolute;
  width: 3px;
  height: 3px;
  background: #ff014f;
  border-radius: 50%;
  animation: portfolioParticleFloat 8s ease-in-out infinite;
}

.portfolio-particles::before {
  top: 15%;
  left: 15%;
  animation-delay: 0s;
}

.portfolio-particles::after {
  top: 70%;
  right: 20%;
  animation-delay: 4s;
}

.portfolio-grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(255, 1, 79, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 1, 79, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: portfolioGridMove 20s linear infinite;
  z-index: -1;
}

.portfolio-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at center,
    rgba(255, 1, 79, 0.1) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.portfolio-card:hover .portfolio-glow {
  opacity: 1;
}

.portfolio-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.portfolio-card:hover .portfolio-particles::before,
.portfolio-card:hover .portfolio-particles::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 2px;
  background: #ff014f;
  border-radius: 50%;
  animation: cardParticleFloat 2s ease-in-out infinite;
}

.portfolio-card:hover .portfolio-particles::before {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.portfolio-card:hover .portfolio-particles::after {
  bottom: 20%;
  right: 20%;
  animation-delay: 1s;
}

/* Enhanced Feature Cards */
.enhanced-feature-card {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 32px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.03) 0%,
    rgba(255, 1, 79, 0.02) 100%
  );
  border-radius: 20px;
  border: 1px solid rgba(255, 1, 79, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: featureCardFadeIn 1s ease-out;
  overflow: hidden;
}

.enhanced-feature-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(255, 1, 79, 0.3);
  box-shadow: 0 20px 40px rgba(255, 1, 79, 0.15);
}

.feature-icon-container {
  position: relative;
  flex-shrink: 0;
}

.feature-icon {
  font-size: 3rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.enhanced-feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: radial-gradient(
    circle,
    rgba(255, 1, 79, 0.2) 0%,
    transparent 70%
  );
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: iconGlowPulse 2s ease-in-out infinite alternate;
}

.enhanced-feature-card:hover .icon-glow {
  opacity: 1;
}

.icon-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.icon-particles::before,
.icon-particles::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 2px;
  background: #ff014f;
  border-radius: 50%;
  opacity: 0;
  animation: iconParticleFloat 3s ease-in-out infinite;
}

.enhanced-feature-card:hover .icon-particles::before,
.enhanced-feature-card:hover .icon-particles::after {
  opacity: 1;
}

.icon-particles::before {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.icon-particles::after {
  top: 80%;
  right: 20%;
  animation-delay: 1.5s;
}

.feature-content {
  flex: 1;
}

.feature-title {
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #c4cfde 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.feature-description {
  color: #c4cfde;
  line-height: 1.6;
  font-size: 0.95rem;
}

.card-glow-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 1, 79, 0.05) 0%,
    transparent 50%
  );
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-feature-card:hover .card-glow-effect {
  opacity: 1;
}

.card-border-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 1, 79, 0.5),
    transparent
  );
  opacity: 0;
  animation: borderSweep 2s ease-in-out infinite;
}

.enhanced-feature-card:hover .card-border-animation {
  opacity: 1;
}

/* About Section Keyframe Animations */
@keyframes aboutBgFloat {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(50px, -30px) scale(1.1);
    opacity: 0.6;
  }
}

@keyframes aboutParticleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-30px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-40px) rotate(270deg);
    opacity: 0.6;
  }
}

@keyframes titleFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes titleGlow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
}

@keyframes underlineSlide {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 80px;
    opacity: 1;
  }
}

@keyframes textBlockFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes keywordGlow {
  0% {
    opacity: 0.6;
    transform: scaleX(0.8);
  }
  100% {
    opacity: 1;
    transform: scaleX(1.2);
  }
}

@keyframes infoItemFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes featureCardFadeIn {
  0% {
    opacity: 0;
    transform: translateX(50px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes iconGlowPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.2;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.3);
    opacity: 0.4;
  }
}

@keyframes iconParticleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-15px) scale(1.5);
    opacity: 0.8;
  }
}

@keyframes borderSweep {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Enhanced Resume Section Styling */

/* Background Effects */
.resume-bg-glow {
  position: absolute;
  top: 15%;
  right: 10%;
  width: 500px;
  height: 500px;
  background: radial-gradient(
    circle,
    rgba(255, 1, 79, 0.06) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: resumeBgFloat 10s ease-in-out infinite alternate;
  z-index: 1;
}

.resume-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.resume-particles::before,
.resume-particles::after {
  content: "";
  position: absolute;
  width: 4px;
  height: 4px;
  background: #ff014f;
  border-radius: 50%;
  animation: resumeParticleFloat 12s ease-in-out infinite;
}

.resume-particles::before {
  top: 25%;
  left: 15%;
  animation-delay: 0s;
}

.resume-particles::after {
  top: 75%;
  right: 20%;
  animation-delay: 6s;
}

.resume-grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(255, 1, 79, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 1, 79, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
  z-index: 1;
}

/* Enhanced Resume Title */
.enhanced-resume-title {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #ff6b9d 30%,
    #ff014f 70%,
    #ffffff 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: resumeTitleFadeIn 1s ease-out;
}

.resume-title-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 100px;
  background: radial-gradient(
    ellipse,
    rgba(255, 1, 79, 0.2) 0%,
    transparent 70%
  );
  animation: resumeTitleGlow 4s ease-in-out infinite alternate;
  z-index: -1;
}

.resume-title-underline {
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 4px;
  background: linear-gradient(
    90deg,
    transparent,
    #ff014f,
    #ff6b9d,
    #ff014f,
    transparent
  );
  border-radius: 2px;
  animation: resumeUnderlineExpand 2s ease-out;
}

/* Enhanced Description */
.resume-description-container {
  margin-top: 24px;
  animation: resumeDescFadeIn 1s ease-out 0.3s both;
}

.resume-description {
  color: #c4cfde;
  font-size: 1.125rem;
  line-height: 1.8;
  max-width: 2xl;
  margin: 0 auto;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Enhanced Section Subtitles */
.enhanced-section-subtitle {
  font-size: 2rem;
  font-weight: 600;
  background: linear-gradient(135deg, #ffffff 0%, #c4cfde 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: subtitleFadeIn 1s ease-out;
}

.subtitle-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 60px;
  background: radial-gradient(
    ellipse,
    rgba(255, 1, 79, 0.15) 0%,
    transparent 70%
  );
  animation: subtitleGlow 3s ease-in-out infinite alternate;
  z-index: -1;
}

.subtitle-line {
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #ff014f 0%, #ff6b9d 100%);
  border-radius: 2px;
  animation: subtitleLineSlide 1.5s ease-out;
}

/* Enhanced Timeline Items */
.timeline-container {
  position: relative;
}

.enhanced-timeline-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 1, 79, 0.03) 100%
  );
  border-radius: 16px;
  border: 1px solid rgba(255, 1, 79, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: timelineItemFadeIn 1s ease-out;
  overflow: hidden;
}

.enhanced-timeline-item:hover {
  transform: translateX(8px) scale(1.02);
  border-color: rgba(255, 1, 79, 0.3);
  box-shadow: 0 12px 30px rgba(255, 1, 79, 0.15);
}

.timeline-connector {
  position: absolute;
  left: 11px;
  top: 60px;
  bottom: -32px;
  width: 2px;
  background: linear-gradient(180deg, #ff014f 0%, rgba(255, 1, 79, 0.3) 100%);
}

.enhanced-timeline-item:last-child .timeline-connector {
  display: none;
}

.timeline-dot {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  border-radius: 50%;
  border: 3px solid rgba(255, 1, 79, 0.2);
  position: relative;
  animation: timelineDotPulse 2s ease-in-out infinite alternate;
}

.timeline-dot::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #ffffff;
  border-radius: 50%;
  animation: dotInnerGlow 1.5s ease-in-out infinite alternate;
}

.timeline-content {
  flex: 1;
}

.period-badge {
  display: inline-block;
  padding: 6px 16px;
  background: linear-gradient(
    135deg,
    rgba(255, 1, 79, 0.15) 0%,
    rgba(255, 107, 157, 0.15) 100%
  );
  color: #ff6b9d;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
  border: 1px solid rgba(255, 1, 79, 0.2);
}

.timeline-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #ffffff 0%, #c4cfde 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.timeline-institution {
  color: #ff014f;
  font-weight: 500;
  margin-bottom: 12px;
  font-size: 1rem;
}

.timeline-description {
  color: #c4cfde;
  line-height: 1.6;
  font-size: 0.95rem;
}

.timeline-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 1, 79, 0.05) 0%,
    transparent 50%
  );
  border-radius: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-timeline-item:hover .timeline-glow {
  opacity: 1;
}

.timeline-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.timeline-particles::before,
.timeline-particles::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 2px;
  background: #ff014f;
  border-radius: 50%;
  opacity: 0;
  animation: timelineParticleFloat 4s ease-in-out infinite;
}

.enhanced-timeline-item:hover .timeline-particles::before,
.enhanced-timeline-item:hover .timeline-particles::after {
  opacity: 1;
}

.timeline-particles::before {
  top: 20%;
  right: 20%;
  animation-delay: 0s;
}

.timeline-particles::after {
  top: 70%;
  right: 40%;
  animation-delay: 2s;
}

/* Enhanced Skills */
.enhanced-skill-item {
  position: relative;
  padding: 20px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 1, 79, 0.02) 100%
  );
  border-radius: 12px;
  border: 1px solid rgba(255, 1, 79, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: skillItemFadeIn 1s ease-out;
  overflow: hidden;
}

.enhanced-skill-item:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 1, 79, 0.3);
  box-shadow: 0 8px 25px rgba(255, 1, 79, 0.15);
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.skill-name {
  color: #ffffff;
  font-weight: 600;
  font-size: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #c4cfde 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.skill-percentage {
  color: #ff014f;
  font-weight: 700;
  font-size: 1rem;
}

.skill-bar-container {
  position: relative;
  margin-bottom: 8px;
}

.skill-bar-bg {
  position: relative;
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.skill-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
  border-radius: 4px;
  position: relative;
  width: 0%;
  transition: width 2.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 10px rgba(255, 1, 79, 0.5);
}

.skill-bar-fill::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: skillBarShimmer 2s ease-in-out infinite;
}

.skill-bar-glow {
  position: absolute;
  top: -2px;
  left: 0;
  width: 0%;
  height: 12px;
  background: linear-gradient(
    90deg,
    rgba(255, 1, 79, 0.6),
    rgba(255, 107, 157, 0.8),
    rgba(255, 1, 79, 0.6)
  );
  border-radius: 6px;
  opacity: 0;
  transition: width 2.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease;
  filter: blur(1px);
  animation: skillGlowPulse 1s ease-in-out infinite alternate;
}

.enhanced-skill-item:hover .skill-bar-glow {
  opacity: 1;
}

/* Enhanced Animation States */
.skill-animating {
  transform: scale(1.02);
  border-color: rgba(255, 1, 79, 0.4);
  box-shadow: 0 12px 30px rgba(255, 1, 79, 0.2);
  transition: all 0.3s ease;
}

.skill-bar-animating {
  animation: skillBarPulse 2.5s ease-in-out;
}

.skill-bar-animating::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: skillBarSweep 2.5s ease-in-out;
}

.skill-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.skill-particles::before,
.skill-particles::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 2px;
  background: #ff014f;
  border-radius: 50%;
  opacity: 0;
  animation: skillParticleFloat 3s ease-in-out infinite;
}

.enhanced-skill-item:hover .skill-particles::before,
.enhanced-skill-item:hover .skill-particles::after {
  opacity: 1;
}

.skill-particles::before {
  top: 30%;
  right: 20%;
  animation-delay: 0s;
}

.skill-particles::after {
  top: 70%;
  right: 40%;
  animation-delay: 1.5s;
}

/* Resume Section Keyframe Animations */
@keyframes resumeBgFloat {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-30px, 40px) scale(1.1);
    opacity: 0.6;
  }
}

@keyframes resumeParticleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-40px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-50px) rotate(270deg);
    opacity: 0.6;
  }
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes resumeTitleFadeIn {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes resumeTitleGlow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.4;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.3);
    opacity: 0.7;
  }
}

@keyframes resumeUnderlineExpand {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 120px;
    opacity: 1;
  }
}

@keyframes resumeDescFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes subtitleFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes subtitleGlow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
}

@keyframes subtitleLineSlide {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 60px;
    opacity: 1;
  }
}

@keyframes timelineItemFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-40px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes timelineDotPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 1, 79, 0.4);
  }
  100% {
    transform: scale(1.1);
    box-shadow: 0 0 0 8px rgba(255, 1, 79, 0);
  }
}

@keyframes dotInnerGlow {
  0% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes timelineParticleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-15px) scale(1.5);
    opacity: 0.8;
  }
}

@keyframes skillItemFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes skillBarShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes skillParticleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px) scale(1.3);
    opacity: 0.8;
  }
}

@keyframes skillGlowPulse {
  0% {
    filter: blur(1px) brightness(1);
  }
  100% {
    filter: blur(2px) brightness(1.3);
  }
}

@keyframes skillBarPulse {
  0%,
  100% {
    box-shadow: 0 0 10px rgba(255, 1, 79, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 1, 79, 0.8), 0 0 30px rgba(255, 1, 79, 0.4);
  }
}

@keyframes skillBarSweep {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes typingPulse {
  0% {
    transform: scaleY(1) scaleX(1);
  }
  50% {
    transform: scaleY(1.1) scaleX(1.5);
  }
  100% {
    transform: scaleY(1) scaleX(1);
  }
}

/* Portfolio Animations */
@keyframes portfolioTitleGlow {
  0% {
    text-shadow: 0 0 20px rgba(255, 1, 79, 0.3);
  }
  100% {
    text-shadow: 0 0 30px rgba(255, 1, 79, 0.6), 0 0 40px rgba(255, 1, 79, 0.4);
  }
}

@keyframes portfolioTitlePulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.6;
  }
}

@keyframes portfolioUnderlineGlow {
  0% {
    box-shadow: 0 0 10px rgba(255, 1, 79, 0.5);
  }
  100% {
    box-shadow: 0 0 20px rgba(255, 1, 79, 0.8), 0 0 30px rgba(255, 1, 79, 0.6);
  }
}

@keyframes filterBtnFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes filterParticleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes portfolioItemFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes portfolioBgGlow {
  0% {
    transform: translateX(-50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translateX(-50%) scale(1.2);
    opacity: 0.6;
  }
}

@keyframes portfolioParticleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.5;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) rotate(270deg);
    opacity: 1;
  }
}

@keyframes portfolioGridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes cardParticleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-15px) scale(1.2);
    opacity: 1;
  }
}
