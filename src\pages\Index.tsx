
import React from 'react';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Hero from '../components/Hero';
import About from '../components/About';
import Resume from '../components/Resume';
import Portfolio from '../components/Portfolio';
import Testimonials from '../components/Testimonials';
import Contact from '../components/Contact';
import Footer from '../components/Footer';

const Index = () => {
  return (
    <div className="min-h-screen flex flex-col lg:flex-row relative">
      {/* Left Sidebar */}
      <Sidebar />

      {/* Main Content */}
      <div className="w-full lg:ml-[380px]">
        {/* Header */}
        <Header />

        {/* Sections */}
        <main>
          <Hero />
          <About />
          <Resume />
          <Portfolio />
          <Testimonials />
          <Contact />
          <Footer />
        </main>
      </div>
    </div>
  );
};

export default Index;
