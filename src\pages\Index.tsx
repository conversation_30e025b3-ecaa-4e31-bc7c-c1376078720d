import React from "react";
import Header from "../components/Header";
import Sidebar from "../components/Sidebar";
import Hero from "../components/Hero";
import About from "../components/About";
import Resume from "../components/Resume";
import Skills from "../components/Skills";
import Portfolio from "../components/Portfolio";
import Testimonials from "../components/Testimonials";
import Contact from "../components/Contact";
import Footer from "../components/Footer";

const Index = () => {
  return (
    <div className="min-h-screen flex flex-col lg:flex-row relative">
      {/* Left Sidebar - Hidden on mobile, fixed on desktop */}
      <div className="hidden lg:block">
        <Sidebar />
      </div>

      {/* Main Content */}
      <div className="w-full lg:ml-[380px]">
        {/* Header */}
        <Header />

        {/* Sections */}
        <main>
          <Hero />
          <About />
          <Resume />
          <Skills />
          <Portfolio />
          <Testimonials />
          <Contact />
          <Footer />
        </main>
      </div>
    </div>
  );
};

export default Index;
