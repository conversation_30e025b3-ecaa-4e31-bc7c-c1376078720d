
import React from 'react';

const About = () => {
  // Features data
  const features = [
    {
      title: "Business Strategy",
      description: "There are many variations of passages of Lorem Ipsum available.",
      icon: "💼"
    },
    {
      title: "Website Development",
      description: "There are many variations of passages of Lorem Ipsum available.",
      icon: "💻"
    },
    {
      title: "Marketing & Reporting",
      description: "There are many variations of passages of Lorem Ipsum available.",
      icon: "📊"
    }
  ];

  return (
    <section id="about" className="section">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row gap-10">
          {/* About Content */}
          <div className="w-full md:w-1/2">
            <h2 className="section-title">About Me</h2>
            
            <div className="mt-8 space-y-4 text-gray-200">
              <p>
                I'm a web developer with a passion for creating beautiful, functional, and user-friendly websites. 
                I have 8+ years of experience in various technologies including React, Next.js, Node.js, and more.
              </p>
              <p>
                I help companies build great products through my expertise in frontend and backend development, 
                focusing on performance, accessibility, and modern design principles.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-8">
              <div>
                <h6 className="text-gray-300 font-medium">Name:</h6>
                <p className="text-gray-200">John Smith</p>
              </div>
              <div>
                <h6 className="text-gray-300 font-medium">Email:</h6>
                <p className="text-gray-200"><EMAIL></p>
              </div>
              <div>
                <h6 className="text-gray-300 font-medium">Phone:</h6>
                <p className="text-gray-200">+************</p>
              </div>
              <div>
                <h6 className="text-gray-300 font-medium">Location:</h6>
                <p className="text-gray-200">New York, USA</p>
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="w-full md:w-1/2">
            <div className="grid grid-cols-1 gap-6">
              {features.map((feature, index) => (
                <div key={index} className="card w-full aspect-auto p-8 flex flex-row items-start gap-4 hover:transform hover:scale-105 transition-all duration-300">
                  <div className="text-4xl mb-4 group-hover:text-red transition-all duration-300">
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="text-gray-300 text-xl font-medium mb-3">{feature.title}</h3>
                    <p className="text-gray-200">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
