import React, { useEffect, useRef } from "react";

const Resume = () => {
  // Animation refs for skill bars
  const skillRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: "0px",
      threshold: 0.3,
    };

    const handleIntersect = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const skillItem = entry.target as HTMLElement;
          const progressBar = skillItem.querySelector(
            ".skill-bar-fill"
          ) as HTMLElement;
          const counter = skillItem.querySelector(
            ".counter-number"
          ) as HTMLElement;
          const skillBarGlow = skillItem.querySelector(
            ".skill-bar-glow"
          ) as HTMLElement;
          const percentage = parseInt(
            counter.getAttribute("data-percentage") || "0",
            10
          );

          // Add stunning animation classes
          skillItem.classList.add("skill-animating");
          progressBar.classList.add("skill-bar-animating");

          // Animate progress bar with stunning effect
          setTimeout(() => {
            progressBar.style.width = `${percentage}%`;
            progressBar.style.transition =
              "width 2.5s cubic-bezier(0.4, 0, 0.2, 1)";

            // Add glow effect during animation
            if (skillBarGlow) {
              skillBarGlow.style.opacity = "1";
              skillBarGlow.style.width = `${percentage}%`;
              skillBarGlow.style.transition =
                "width 2.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease";
            }
          }, 200);

          // Animate the counter with enhanced timing
          let count = 0;
          const duration = 2500; // ms - longer for more dramatic effect
          const interval = 30; // ms - smoother animation
          const increment = Math.ceil(percentage / (duration / interval));

          const timer = setInterval(() => {
            count = Math.min(count + increment, percentage);
            counter.textContent = `${count}%`;

            // Add pulsing effect during counting
            counter.style.transform = `scale(${
              1 + (count / percentage) * 0.1
            })`;
            counter.style.color = `hsl(${
              340 + (count / percentage) * 20
            }, 100%, ${60 + (count / percentage) * 10}%)`;

            if (count >= percentage) {
              clearInterval(timer);
              counter.textContent = `${percentage}%`;

              // Final effect when complete
              counter.style.transform = "scale(1.1)";
              counter.style.color = "#ff014f";

              setTimeout(() => {
                counter.style.transform = "scale(1)";
                counter.style.transition = "transform 0.3s ease";
              }, 200);

              // Hide glow after animation
              setTimeout(() => {
                if (skillBarGlow) {
                  skillBarGlow.style.opacity = "0";
                }
              }, 1000);
            }
          }, interval);
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersect, observerOptions);

    skillRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => {
      skillRefs.current.forEach((ref) => {
        if (ref) observer.unobserve(ref);
      });
    };
  }, []);

  // Education data
  const education = [
    {
      degree: "BSC in Computer Science",
      institution: "Harvard University",
      period: "2015-2019",
      description:
        "Computer Science is the study of computers and computational systems focusing on software and software systems.",
    },
    {
      degree: "Higher Secondary Certificate",
      institution: "New York High School",
      period: "2013-2015",
      description:
        "Higher Secondary Education is a prerequisite for the pursuit of higher education after completing secondary education.",
    },
    {
      degree: "Secondary School Certificate",
      institution: "New York Secondary School",
      period: "2010-2013",
      description:
        "Secondary education or post-primary education covers two phases on the International Standard Classification of Education scale.",
    },
  ];

  // Experience data
  const experience = [
    {
      position: "Sr. Software Engineer",
      company: "Google Inc.",
      period: "2021-Present",
      description:
        "Google's hiring process is designed to hire the most talented, creative, and articulate people in the world who will be the best fit for Google.",
    },
    {
      position: "Web Developer & Trainer",
      company: "Apple Inc.",
      period: "2020-2021",
      description:
        "Apple Inc. is an American multinational technology company that specializes in consumer electronics, computer software, and online services.",
    },
    {
      position: "Front-End Developer",
      company: "Adobe Inc.",
      period: "2019-2020",
      description:
        "Adobe Inc. is an American multinational computer software company focused on multimedia and creativity software products.",
    },
  ];

  // Skills data with the specified percentages
  const skills = [
    { name: "HTML & CSS", percentage: 95 },
    { name: "JavaScript", percentage: 89 },
    { name: "React.js", percentage: 90 },
    { name: "Node.js", percentage: 82 },
    { name: "WordPress", percentage: 95 },
    { name: "Figma", percentage: 88 },
  ];

  return (
    <section
      id="resume"
      className="section bg-dark-darker relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="resume-bg-glow"></div>
      <div className="resume-particles"></div>
      <div className="resume-grid-pattern"></div>

      <div className="container mx-auto relative z-10">
        {/* Enhanced Title Section */}
        <div className="text-center mb-16">
          <div className="relative inline-block">
            <h2 className="enhanced-resume-title">My Resume</h2>
            <div className="resume-title-glow"></div>
            <div className="resume-title-underline"></div>
          </div>

          <div className="resume-description-container">
            <p className="resume-description">
              <span className="highlight-text">Experience</span> and{" "}
              <span className="highlight-text">education</span> have been the
              <span className="highlight-text">pillars</span> of my professional
              career, helping me build
              <span className="highlight-text">expertise</span> in web
              development and design.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {/* Enhanced Education */}
          <div className="education-section">
            <div className="relative mb-10">
              <h3 className="enhanced-section-subtitle">Education</h3>
              <div className="subtitle-glow"></div>
              <div className="subtitle-line"></div>
            </div>

            <div className="timeline-container">
              {education.map((item, index) => (
                <div
                  key={index}
                  className="enhanced-timeline-item education-item"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="timeline-connector"></div>
                  <div className="timeline-dot"></div>

                  <div className="timeline-content">
                    <div className="period-badge">{item.period}</div>
                    <h4 className="timeline-title">{item.degree}</h4>
                    <p className="timeline-institution">{item.institution}</p>
                    <p className="timeline-description">{item.description}</p>
                  </div>

                  <div className="timeline-glow"></div>
                  <div className="timeline-particles"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced Experience */}
          <div className="experience-section">
            <div className="relative mb-10">
              <h3 className="enhanced-section-subtitle">Experience</h3>
              <div className="subtitle-glow"></div>
              <div className="subtitle-line"></div>
            </div>

            <div className="timeline-container">
              {experience.map((item, index) => (
                <div
                  key={index}
                  className="enhanced-timeline-item experience-item"
                  style={{ animationDelay: `${index * 0.2 + 0.6}s` }}
                >
                  <div className="timeline-connector"></div>
                  <div className="timeline-dot"></div>

                  <div className="timeline-content">
                    <div className="period-badge">{item.period}</div>
                    <h4 className="timeline-title">{item.position}</h4>
                    <p className="timeline-institution">{item.company}</p>
                    <p className="timeline-description">{item.description}</p>
                  </div>

                  <div className="timeline-glow"></div>
                  <div className="timeline-particles"></div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Enhanced Skills */}
        <div className="mt-20">
          <div className="relative mb-12 text-center">
            <h3 className="enhanced-section-subtitle">Professional Skills</h3>
            <div className="subtitle-glow mx-auto"></div>
            <div className="subtitle-line mx-auto"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-10 gap-y-8">
            {skills.map((skill, index) => (
              <div
                key={index}
                ref={(el) => (skillRefs.current[index] = el)}
                className="enhanced-skill-item"
                style={{ animationDelay: `${index * 0.1 + 1.2}s` }}
              >
                <div className="skill-header">
                  <h4 className="skill-name">{skill.name}</h4>
                  <span
                    className="skill-percentage counter-number"
                    data-percentage={skill.percentage}
                  >
                    0%
                  </span>
                </div>

                <div className="skill-bar-container">
                  <div className="skill-bar-bg">
                    <div
                      className="skill-bar-fill progress-bar"
                      style={{ width: "0%" }}
                      data-percentage={skill.percentage}
                    ></div>
                    <div className="skill-bar-glow"></div>
                  </div>
                </div>

                <div className="skill-particles"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Resume;
