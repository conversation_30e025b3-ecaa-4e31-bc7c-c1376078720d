
import React, { useEffect, useRef } from 'react';

const Resume = () => {
  // Animation refs for skill bars
  const skillRefs = useRef<(HTMLDivElement | null)[]>([]);
  
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.3,
    };

    const handleIntersect = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const skillItem = entry.target as HTMLElement;
          const progressBar = skillItem.querySelector('.progress-bar') as HTMLElement;
          const counter = skillItem.querySelector('.counter-number') as HTMLElement;
          const percentage = parseInt(counter.getAttribute('data-percentage') || '0', 10);
          
          // Add animation class
          skillItem.classList.add('animated');
          
          // Set progress bar width using CSS variable
          progressBar.style.setProperty('--percentage', `${percentage}%`);
          
          // Animate the counter
          let count = 0;
          const duration = 1500; // ms
          const interval = 20; // ms
          const increment = Math.ceil(percentage / (duration / interval));
          
          const timer = setInterval(() => {
            count = Math.min(count + increment, percentage);
            counter.textContent = `${count}%`;
            
            if (count >= percentage) {
              clearInterval(timer);
              counter.textContent = `${percentage}%`;
            }
          }, interval);
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersect, observerOptions);
    
    skillRefs.current.forEach(ref => {
      if (ref) observer.observe(ref);
    });

    return () => {
      skillRefs.current.forEach(ref => {
        if (ref) observer.unobserve(ref);
      });
    };
  }, []);

  // Education data
  const education = [
    {
      degree: "BSC in Computer Science",
      institution: "Harvard University",
      period: "2015-2019",
      description: "Computer Science is the study of computers and computational systems focusing on software and software systems."
    },
    {
      degree: "Higher Secondary Certificate",
      institution: "New York High School",
      period: "2013-2015",
      description: "Higher Secondary Education is a prerequisite for the pursuit of higher education after completing secondary education."
    },
    {
      degree: "Secondary School Certificate",
      institution: "New York Secondary School",
      period: "2010-2013",
      description: "Secondary education or post-primary education covers two phases on the International Standard Classification of Education scale."
    },
  ];

  // Experience data
  const experience = [
    {
      position: "Sr. Software Engineer",
      company: "Google Inc.",
      period: "2021-Present",
      description: "Google's hiring process is designed to hire the most talented, creative, and articulate people in the world who will be the best fit for Google."
    },
    {
      position: "Web Developer & Trainer",
      company: "Apple Inc.",
      period: "2020-2021",
      description: "Apple Inc. is an American multinational technology company that specializes in consumer electronics, computer software, and online services."
    },
    {
      position: "Front-End Developer",
      company: "Adobe Inc.",
      period: "2019-2020",
      description: "Adobe Inc. is an American multinational computer software company focused on multimedia and creativity software products."
    }
  ];

  // Skills data with the specified percentages
  const skills = [
    { name: "HTML & CSS", percentage: 95 },
    { name: "JavaScript", percentage: 89 },
    { name: "React.js", percentage: 90 },
    { name: "Node.js", percentage: 82 },
    { name: "WordPress", percentage: 95 },
    { name: "Figma", percentage: 88 },
  ];

  return (
    <section id="resume" className="section bg-dark-darker">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="heading-title">My Resume</h2>
          <p className="text-gray-200 max-w-2xl mx-auto">
            Experience and education have been the pillars of my professional career, helping me build expertise in web development and design.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Education */}
          <div>
            <h3 className="section-title mb-8">Education</h3>
            <div className="space-y-6">
              {education.map((item, index) => (
                <div key={index} className="card relative pl-8 before:absolute before:left-0 before:top-0 before:h-full before:w-[2px] before:bg-red">
                  <span className="inline-block px-4 py-1 bg-red/10 text-red rounded text-sm mb-2">{item.period}</span>
                  <h4 className="text-xl text-gray-300 font-semibold mb-1">{item.degree}</h4>
                  <p className="text-red mb-3">{item.institution}</p>
                  <p className="text-gray-200">{item.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Experience */}
          <div>
            <h3 className="section-title mb-8">Experience</h3>
            <div className="space-y-6">
              {experience.map((item, index) => (
                <div key={index} className="card relative pl-8 before:absolute before:left-0 before:top-0 before:h-full before:w-[2px] before:bg-red">
                  <span className="inline-block px-4 py-1 bg-red/10 text-red rounded text-sm mb-2">{item.period}</span>
                  <h4 className="text-xl text-gray-300 font-semibold mb-1">{item.position}</h4>
                  <p className="text-red mb-3">{item.company}</p>
                  <p className="text-gray-200">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Skills */}
        <div className="mt-16">
          <h3 className="section-title mb-8">Professional Skills</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
            {skills.map((skill, index) => (
              <div 
                key={index} 
                ref={el => skillRefs.current[index] = el}
                className="skill-item"
              >
                <div className="flex justify-between mb-2">
                  <h4 className="text-gray-300 font-medium">{skill.name}</h4>
                  <span className="text-red counter-number" data-percentage={skill.percentage}>0%</span>
                </div>
                <div className="w-full h-1 bg-dark-bg rounded-full">
                  <div 
                    className="progress-bar h-full bg-red-light rounded-full" 
                    style={{ width: '0%' }}
                    data-percentage={skill.percentage}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Resume;
