import React from "react";

const Resume = () => {
  // Education data
  const education = [
    {
      degree: "BSC in Computer Science",
      institution: "Harvard University",
      period: "2015-2019",
      description:
        "Computer Science is the study of computers and computational systems focusing on software and software systems.",
    },
    {
      degree: "Higher Secondary Certificate",
      institution: "New York High School",
      period: "2013-2015",
      description:
        "Higher Secondary Education is a prerequisite for the pursuit of higher education after completing secondary education.",
    },
    {
      degree: "Secondary School Certificate",
      institution: "New York Secondary School",
      period: "2010-2013",
      description:
        "Secondary education or post-primary education covers two phases on the International Standard Classification of Education scale.",
    },
  ];

  // Experience data
  const experience = [
    {
      position: "Sr. Software Engineer",
      company: "Google Inc.",
      period: "2021-Present",
      description:
        "Google's hiring process is designed to hire the most talented, creative, and articulate people in the world who will be the best fit for Google.",
    },
    {
      position: "Web Developer & Trainer",
      company: "Apple Inc.",
      period: "2020-2021",
      description:
        "Apple Inc. is an American multinational technology company that specializes in consumer electronics, computer software, and online services.",
    },
    {
      position: "Front-End Developer",
      company: "Adobe Inc.",
      period: "2019-2020",
      description:
        "Adobe Inc. is an American multinational computer software company focused on multimedia and creativity software products.",
    },
  ];

  return (
    <section
      id="resume"
      className="section bg-dark-darker relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="resume-bg-glow"></div>
      <div className="resume-particles"></div>
      <div className="resume-grid-pattern"></div>

      <div className="container mx-auto relative z-10">
        {/* Enhanced Title Section */}
        <div className="text-center mb-16">
          <div className="relative inline-block">
            <h2 className="enhanced-resume-title">My Resume</h2>
            <div className="resume-title-glow"></div>
            <div className="resume-title-underline"></div>
          </div>

          <div className="resume-description-container">
            <p className="resume-description">
              <span className="highlight-text">Experience</span> and{" "}
              <span className="highlight-text">education</span> have been the
              <span className="highlight-text">pillars</span> of my professional
              career, helping me build
              <span className="highlight-text">expertise</span> in web
              development and design.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {/* Enhanced Education */}
          <div className="education-section">
            <div className="relative mb-10">
              <h3 className="enhanced-section-subtitle">Education</h3>
              <div className="subtitle-glow"></div>
              <div className="subtitle-line"></div>
            </div>

            <div className="timeline-container">
              {education.map((item, index) => (
                <div
                  key={index}
                  className="enhanced-timeline-item education-item"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="timeline-connector"></div>
                  <div className="timeline-dot"></div>

                  <div className="timeline-content">
                    <div className="period-badge">{item.period}</div>
                    <h4 className="timeline-title">{item.degree}</h4>
                    <p className="timeline-institution">{item.institution}</p>
                    <p className="timeline-description">{item.description}</p>
                  </div>

                  <div className="timeline-glow"></div>
                  <div className="timeline-particles"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced Experience */}
          <div className="experience-section">
            <div className="relative mb-10">
              <h3 className="enhanced-section-subtitle">Experience</h3>
              <div className="subtitle-glow"></div>
              <div className="subtitle-line"></div>
            </div>

            <div className="timeline-container">
              {experience.map((item, index) => (
                <div
                  key={index}
                  className="enhanced-timeline-item experience-item"
                  style={{ animationDelay: `${index * 0.2 + 0.6}s` }}
                >
                  <div className="timeline-connector"></div>
                  <div className="timeline-dot"></div>

                  <div className="timeline-content">
                    <div className="period-badge">{item.period}</div>
                    <h4 className="timeline-title">{item.position}</h4>
                    <p className="timeline-institution">{item.company}</p>
                    <p className="timeline-description">{item.description}</p>
                  </div>

                  <div className="timeline-glow"></div>
                  <div className="timeline-particles"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Resume;
